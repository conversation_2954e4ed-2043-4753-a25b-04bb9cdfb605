#!/usr/bin/env python3
"""
Financial Sentiment Analyzer - Main Entry Point

A comprehensive financial sentiment analysis tool with intelligent caching
and interactive dashboard capabilities.

Usage:
    python main.py [--quick] [--verbose] [--dashboard]
    
Options:
    --quick     : Quick analysis (20 tickers instead of 130+)
    --verbose   : Enable verbose logging
    --dashboard : Launch interactive dashboard directly
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Main entry point for the Financial Sentiment Analyzer."""
    try:
        from src.core.financial_analyzer import main as analyzer_main
        analyzer_main()
    except ImportError as e:
        print(f"Error importing modules: {e}")
        print("Please ensure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error running application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
