#!/usr/bin/env python3
"""
Comprehensive test runner for the Financial Sentiment Analyzer.

This script provides multiple ways to run tests:
- All tests
- Specific test categories (unit, integration, etc.)
- Individual test modules
- With coverage reporting
- With performance profiling

Usage:
    python tests/test_runner.py                    # Run all tests
    python tests/test_runner.py --unit             # Run only unit tests
    python tests/test_runner.py --integration      # Run only integration tests
    python tests/test_runner.py --coverage         # Run with coverage report
    python tests/test_runner.py --module sentiment # Run specific module tests
    python tests/test_runner.py --verbose          # Verbose output
    python tests/test_runner.py --quick            # Skip slow tests
"""

import sys
import os
import argparse
import subprocess
import time
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_command(cmd, description=""):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    if description:
        print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Running: {' '.join(cmd)}")
    print("-" * 60)
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=False, text=True)
    end_time = time.time()
    
    print(f"\n⏱️  Completed in {end_time - start_time:.2f} seconds")
    print(f"✅ Exit code: {result.returncode}")
    
    return result.returncode == 0

def run_unittest_discovery():
    """Run tests using unittest discovery."""
    cmd = [sys.executable, '-m', 'unittest', 'discover', '-s', 'tests', '-p', 'test_*.py', '-v']
    return run_command(cmd, "Running Unit Tests with unittest")

def run_pytest(args=None):
    """Run tests using pytest."""
    cmd = [sys.executable, '-m', 'pytest']
    if args:
        cmd.extend(args)
    return run_command(cmd, "Running Tests with pytest")

def run_specific_module(module_name):
    """Run tests for a specific module."""
    test_file = f"tests/test_{module_name}.py"
    if os.path.exists(test_file):
        cmd = [sys.executable, '-m', 'pytest', test_file, '-v']
        return run_command(cmd, f"Running {module_name} tests")
    else:
        print(f"❌ Test file not found: {test_file}")
        return False

def run_with_coverage():
    """Run tests with coverage reporting."""
    try:
        import pytest_cov
        cmd = [
            sys.executable, '-m', 'pytest',
            '--cov=src',
            '--cov-report=html',
            '--cov-report=term-missing',
            '--cov-report=xml',
            '-v'
        ]
        success = run_command(cmd, "Running Tests with Coverage")
        
        if success:
            print("\n📊 Coverage Report Generated:")
            print("   - HTML: htmlcov/index.html")
            print("   - XML: coverage.xml")
            print("   - Terminal output above")
        
        return success
    except ImportError:
        print("❌ pytest-cov not installed. Install with: pip install pytest-cov")
        return False

def check_test_environment():
    """Check if the test environment is properly set up."""
    print("🔍 Checking Test Environment...")
    
    # Check if required packages are available
    required_packages = ['pytest', 'unittest', 'textblob', 'yfinance']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - Available")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    # Check if test files exist
    test_files = [
        'tests/test_sentiment_analyzer.py',
        'tests/test_earnings_fetcher.py',
        'tests/test_cache_manager.py',
        'tests/conftest.py'
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"✅ {test_file} - Found")
        else:
            print(f"❌ {test_file} - Missing")
    
    # Check if source modules can be imported
    try:
        from src.core import sentiment_analyzer, earnings_fetcher
        from src.data import cache_manager
        print("✅ Source modules - Importable")
    except ImportError as e:
        print(f"❌ Source modules - Import error: {e}")
        return False
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("\n✅ Test environment is ready!")
    return True

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description='Financial Sentiment Analyzer Test Runner')
    parser.add_argument('--unit', action='store_true', help='Run only unit tests')
    parser.add_argument('--integration', action='store_true', help='Run only integration tests')
    parser.add_argument('--coverage', action='store_true', help='Run tests with coverage')
    parser.add_argument('--module', type=str, help='Run tests for specific module (e.g., sentiment_analyzer)')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--quick', action='store_true', help='Skip slow tests')
    parser.add_argument('--check', action='store_true', help='Check test environment only')
    parser.add_argument('--unittest', action='store_true', help='Use unittest instead of pytest')
    
    args = parser.parse_args()
    
    print("🧪 Financial Sentiment Analyzer Test Suite")
    print("=" * 60)
    
    # Check environment first
    if not check_test_environment():
        print("\n❌ Test environment check failed!")
        return 1
    
    if args.check:
        print("\n✅ Environment check completed successfully!")
        return 0
    
    success = True
    
    try:
        if args.coverage:
            success = run_with_coverage()
        elif args.module:
            success = run_specific_module(args.module)
        elif args.unittest:
            success = run_unittest_discovery()
        else:
            # Build pytest arguments
            pytest_args = []
            
            if args.unit:
                pytest_args.extend(['-m', 'unit'])
            elif args.integration:
                pytest_args.extend(['-m', 'integration'])
            
            if args.verbose:
                pytest_args.append('-vv')
            
            if args.quick:
                pytest_args.extend(['-m', 'not slow'])
            
            success = run_pytest(pytest_args)
        
        # Summary
        print("\n" + "=" * 60)
        if success:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Your Financial Sentiment Analyzer is working correctly!")
        else:
            print("❌ SOME TESTS FAILED!")
            print("🔧 Please review the output above and fix any issues.")
        print("=" * 60)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error running tests: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
