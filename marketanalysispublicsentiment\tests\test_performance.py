"""
Performance tests for the Financial Sentiment Analyzer.

Tests performance characteristics including:
- Large dataset processing
- Memory usage optimization
- Cache performance
- Concurrent operations
- Response time benchmarks
"""

import pytest
import unittest
import time
import sys
import os
from unittest.mock import patch, Mock
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.sentiment_analyzer import analyze_sentiment_batch, analyze_multi_ticker_sentiment
from src.data.cache_manager import CacheManager
from src.core.earnings_fetcher import get_multiple_ticker_earnings


class TestPerformance(unittest.TestCase):
    """Performance test cases."""
    
    def setUp(self):
        """Set up performance test fixtures."""
        # Create large dataset for testing
        self.large_article_dataset = []
        for i in range(1000):
            self.large_article_dataset.append({
                'headline': f'Market News Article {i}',
                'text': f'This is article {i} about market conditions and stock performance. ' * 10,
                'ticker': f'TICK{i % 100}',  # 100 different tickers
                'url': f'https://example.com/article{i}',
                'datetime': '2025-01-15 10:30:00'
            })
        
        # Medium dataset
        self.medium_article_dataset = self.large_article_dataset[:100]
        
        # Small dataset
        self.small_article_dataset = self.large_article_dataset[:10]
    
    def test_sentiment_analysis_performance_small(self):
        """Test sentiment analysis performance with small dataset."""
        start_time = time.time()
        
        scores, details = analyze_sentiment_batch(self.small_article_dataset)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process 10 articles quickly (< 2 seconds)
        self.assertLess(processing_time, 2.0)
        self.assertEqual(len(scores), 10)
        self.assertEqual(len(details), 10)
        
        print(f"Small dataset (10 articles): {processing_time:.3f} seconds")
    
    def test_sentiment_analysis_performance_medium(self):
        """Test sentiment analysis performance with medium dataset."""
        start_time = time.time()
        
        scores, details = analyze_sentiment_batch(self.medium_article_dataset)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process 100 articles in reasonable time (< 10 seconds)
        self.assertLess(processing_time, 10.0)
        self.assertEqual(len(scores), 100)
        self.assertEqual(len(details), 100)
        
        print(f"Medium dataset (100 articles): {processing_time:.3f} seconds")
    
    @pytest.mark.slow
    def test_sentiment_analysis_performance_large(self):
        """Test sentiment analysis performance with large dataset."""
        start_time = time.time()
        
        scores, details = analyze_sentiment_batch(self.large_article_dataset)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process 1000 articles in reasonable time (< 60 seconds)
        self.assertLess(processing_time, 60.0)
        self.assertEqual(len(scores), 1000)
        self.assertEqual(len(details), 1000)
        
        # Calculate articles per second
        articles_per_second = len(self.large_article_dataset) / processing_time
        self.assertGreater(articles_per_second, 10)  # At least 10 articles/second
        
        print(f"Large dataset (1000 articles): {processing_time:.3f} seconds")
        print(f"Processing rate: {articles_per_second:.1f} articles/second")
    
    def test_multi_ticker_sentiment_performance(self):
        """Test multi-ticker sentiment analysis performance."""
        start_time = time.time()
        
        scores, details, multi_articles = analyze_multi_ticker_sentiment(self.medium_article_dataset)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should complete in reasonable time
        self.assertLess(processing_time, 15.0)
        self.assertEqual(len(scores), 100)
        
        print(f"Multi-ticker analysis (100 articles): {processing_time:.3f} seconds")
    
    def test_cache_performance_write_speed(self):
        """Test cache write performance."""
        import tempfile
        import shutil
        
        temp_dir = tempfile.mkdtemp()
        cache_manager = CacheManager(cache_dir=temp_dir)
        
        try:
            # Test writing many cache entries
            start_time = time.time()
            
            for i in range(100):
                test_data = {
                    'id': i,
                    'data': f'test data {i}' * 100  # Larger data
                }
                cache_manager.cache_data('performance_test', f'key_{i}', test_data)
            
            end_time = time.time()
            write_time = end_time - start_time
            
            # Should write 100 entries quickly
            self.assertLess(write_time, 5.0)
            
            writes_per_second = 100 / write_time
            self.assertGreater(writes_per_second, 20)  # At least 20 writes/second
            
            print(f"Cache write performance: {writes_per_second:.1f} writes/second")
            
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_cache_performance_read_speed(self):
        """Test cache read performance."""
        import tempfile
        import shutil
        
        temp_dir = tempfile.mkdtemp()
        cache_manager = CacheManager(cache_dir=temp_dir)
        
        try:
            # Pre-populate cache
            for i in range(100):
                test_data = {'id': i, 'data': f'test data {i}'}
                cache_manager.cache_data('performance_test', f'key_{i}', test_data)
            
            # Test reading performance
            start_time = time.time()
            
            for i in range(100):
                data = cache_manager.get_cached_data('performance_test', f'key_{i}')
                self.assertIsNotNone(data)
            
            end_time = time.time()
            read_time = end_time - start_time
            
            # Should read 100 entries quickly
            self.assertLess(read_time, 2.0)
            
            reads_per_second = 100 / read_time
            self.assertGreater(reads_per_second, 50)  # At least 50 reads/second
            
            print(f"Cache read performance: {reads_per_second:.1f} reads/second")
            
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_concurrent_sentiment_analysis(self):
        """Test concurrent sentiment analysis performance."""
        def analyze_batch(articles):
            return analyze_sentiment_batch(articles)
        
        # Split dataset into chunks for concurrent processing
        chunk_size = 25
        chunks = [
            self.medium_article_dataset[i:i + chunk_size] 
            for i in range(0, len(self.medium_article_dataset), chunk_size)
        ]
        
        start_time = time.time()
        
        # Process chunks concurrently
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(analyze_batch, chunk) for chunk in chunks]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        concurrent_time = end_time - start_time
        
        # Verify all articles were processed
        total_scores = sum(len(result[0]) for result in results)
        self.assertEqual(total_scores, 100)
        
        # Should be faster than sequential processing (though not always guaranteed)
        print(f"Concurrent processing (4 threads): {concurrent_time:.3f} seconds")
        
        # Test sequential processing for comparison
        start_time = time.time()
        sequential_scores, _ = analyze_sentiment_batch(self.medium_article_dataset)
        end_time = time.time()
        sequential_time = end_time - start_time
        
        print(f"Sequential processing: {sequential_time:.3f} seconds")
        print(f"Speedup ratio: {sequential_time / concurrent_time:.2f}x")
    
    def test_memory_usage_large_dataset(self):
        """Test memory usage with large datasets."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process large dataset
        scores, details = analyze_sentiment_batch(self.large_article_dataset)
        
        # Get memory usage after processing
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (< 100MB for 1000 articles)
        self.assertLess(memory_increase, 100)
        
        print(f"Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB")
        print(f"Memory increase: {memory_increase:.1f}MB")
        
        # Verify results
        self.assertEqual(len(scores), 1000)
        self.assertEqual(len(details), 1000)
    
    @patch('src.core.earnings_fetcher.cached_get_ticker_quarterly_earnings')
    def test_earnings_fetcher_performance(self, mock_cached_get):
        """Test earnings fetcher performance with multiple tickers."""
        # Mock earnings data
        mock_earnings = {
            'ticker': 'TEST',
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 100, 'net_income': 10}
            ]
        }
        mock_cached_get.return_value = mock_earnings
        
        # Test with many tickers
        tickers = [f'TICK{i}' for i in range(50)]
        
        start_time = time.time()
        result = get_multiple_ticker_earnings(tickers)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should process 50 tickers quickly (parallel processing)
        self.assertLess(processing_time, 10.0)
        self.assertEqual(len(result), 50)
        
        tickers_per_second = 50 / processing_time
        print(f"Earnings fetching: {tickers_per_second:.1f} tickers/second")
    
    def test_cache_hit_ratio_performance(self):
        """Test cache hit ratio impact on performance."""
        import tempfile
        import shutil
        
        temp_dir = tempfile.mkdtemp()
        cache_manager = CacheManager(cache_dir=temp_dir)
        
        try:
            # Simulate cache misses (first access)
            start_time = time.time()
            for i in range(50):
                def fetch_data():
                    time.sleep(0.01)  # Simulate API call delay
                    return {'data': f'fetched_{i}'}
                
                result, cache_hit = cache_manager.get_or_fetch('test', f'key_{i}', fetch_data)
                self.assertFalse(cache_hit)  # Should be cache miss
            
            miss_time = time.time() - start_time
            
            # Simulate cache hits (second access)
            start_time = time.time()
            for i in range(50):
                def fetch_data():
                    time.sleep(0.01)  # This shouldn't be called
                    return {'data': f'fetched_{i}'}
                
                result, cache_hit = cache_manager.get_or_fetch('test', f'key_{i}', fetch_data)
                self.assertTrue(cache_hit)  # Should be cache hit
            
            hit_time = time.time() - start_time
            
            # Cache hits should be much faster
            speedup = miss_time / hit_time
            self.assertGreater(speedup, 5)  # At least 5x faster
            
            print(f"Cache miss time: {miss_time:.3f}s")
            print(f"Cache hit time: {hit_time:.3f}s")
            print(f"Cache speedup: {speedup:.1f}x")
            
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)


class TestPerformanceBenchmarks(unittest.TestCase):
    """Performance benchmark tests."""
    
    def test_sentiment_analysis_benchmark(self):
        """Benchmark sentiment analysis performance."""
        test_sizes = [10, 50, 100, 500]
        
        print("\n=== Sentiment Analysis Benchmark ===")
        print("Articles | Time (s) | Rate (art/s)")
        print("-" * 35)
        
        for size in test_sizes:
            articles = [
                {
                    'headline': f'Test Article {i}',
                    'text': f'This is test article {i} with market sentiment content.',
                    'ticker': 'TEST'
                }
                for i in range(size)
            ]
            
            start_time = time.time()
            scores, details = analyze_sentiment_batch(articles)
            end_time = time.time()
            
            processing_time = end_time - start_time
            rate = size / processing_time
            
            print(f"{size:8d} | {processing_time:8.3f} | {rate:10.1f}")
            
            # Verify correctness
            self.assertEqual(len(scores), size)
            self.assertEqual(len(details), size)


if __name__ == '__main__':
    # Run with pytest to use markers
    unittest.main()
