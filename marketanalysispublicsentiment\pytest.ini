[tool:pytest]
# Pytest configuration for Financial Sentiment Analyzer

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests with external dependencies
    slow: Tests that take longer to run
    cache: Tests related to caching functionality
    sentiment: Tests related to sentiment analysis
    earnings: Tests related to earnings data
    api: Tests that make external API calls
    
# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if pytest-cov is installed)
# addopts = --cov=src --cov-report=html --cov-report=term-missing

# Ignore certain warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:yfinance.*
