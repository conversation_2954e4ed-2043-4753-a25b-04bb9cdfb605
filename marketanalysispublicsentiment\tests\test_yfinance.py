#!/usr/bin/env python3
"""
Test script to diagnose yfinance news fetching issues
"""

import sys
import os
# Add the parent directory to the path so we can import from src
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yfinance as yf
from datetime import datetime

def test_yfinance_news():
    """Test yfinance news fetching for a few major tickers"""
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    print("🔍 Testing yfinance news fetching...")
    print("=" * 50)
    
    total_articles = 0
    
    for ticker in test_tickers:
        try:
            print(f"\n📊 Testing {ticker}...")
            stock = yf.Ticker(ticker)
            
            # Test basic info first
            try:
                info = stock.info
                print(f"  ✅ Basic info available: {info.get('shortName', 'N/A')}")
            except Exception as e:
                print(f"  ❌ Basic info failed: {e}")
            
            # Test news fetching
            try:
                news = stock.news
                print(f"  📰 News articles found: {len(news) if news else 0}")
                
                if news:
                    total_articles += len(news)
                    # Show first article details
                    first_article = news[0]
                    print(f"  📄 First article: {first_article.get('title', 'No title')[:60]}...")
                    print(f"  🕒 Published: {datetime.fromtimestamp(first_article.get('providerPublishTime', 0))}")
                else:
                    print(f"  ⚠️ No news articles returned for {ticker}")
                    
            except Exception as e:
                print(f"  ❌ News fetching failed: {e}")
                print(f"  🔍 Exception type: {type(e).__name__}")
                print(f"  📝 Full error details: {str(e)}")

                # Try to get more details about the response
                try:
                    import traceback
                    print(f"  📋 Traceback:")
                    traceback.print_exc()
                except Exception as inner_e:
                    print(f"  🔍 Could not get traceback: {inner_e}")
                
        except Exception as e:
            print(f"  ❌ Ticker {ticker} failed completely: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 SUMMARY:")
    print(f"   Total articles found: {total_articles}")
    print(f"   Average per ticker: {total_articles / len(test_tickers):.1f}")
    
    if total_articles == 0:
        print("\n❌ ISSUE IDENTIFIED: No news articles found!")
        print("   Possible causes:")
        print("   1. Yahoo Finance API changes")
        print("   2. Rate limiting")
        print("   3. Network connectivity issues")
        print("   4. yfinance library version issues")
        
        # Test yfinance version
        try:
            import yfinance
            print(f"   📦 yfinance version: {yfinance.__version__}")
        except:
            print("   📦 yfinance version: Unknown")
    else:
        print("\n✅ yfinance news fetching is working!")

if __name__ == "__main__":
    test_yfinance_news()
