#!/usr/bin/env python3
"""
Debug Dominion Energy (D) earnings trends calculation
to understand the 421% income trend figure
"""

from earnings_fetcher import get_earnings_summary_for_ticker

def debug_dominion_calculation():
    """Debug the Dominion Energy calculation step by step"""
    
    print("🔍 DEBUGGING DOMINION ENERGY (D) - 421% Income Trend")
    print("=" * 70)
    
    # Get real earnings data
    earnings_summary = get_earnings_summary_for_ticker("D")
    
    if earnings_summary.get('status') != 'success':
        print("❌ Could not get earnings data for D")
        return
    
    # Extract quarterly data
    quarters = earnings_summary.get('raw_data', {}).get('quarters', [])
    
    print("\n📊 DOMINION ENERGY QUARTERLY DATA:")
    print("=" * 50)
    
    for i, quarter in enumerate(quarters[:4]):
        revenue = quarter.get('metrics', {}).get('revenue', 0)
        net_income = quarter.get('metrics', {}).get('net_income', 0)
        
        revenue_str = f"${revenue/1e9:.1f}B" if revenue > 1e9 else f"${revenue/1e6:.0f}M"
        
        # Handle negative income properly
        if net_income >= 0:
            income_str = f"${net_income/1e6:.0f}M"
        else:
            income_str = f"-${abs(net_income)/1e6:.0f}M"
        
        print(f"   {quarter.get('quarter', 'Unknown')}: Revenue {revenue_str}, Net Income {income_str}")
    
    print("\n🧮 STEP-BY-STEP INCOME TREND CALCULATION:")
    print("=" * 50)
    
    # Manual calculation to show the issue
    if len(quarters) >= 3:
        q1_2025 = quarters[0]  # Most recent
        q4_2024 = quarters[1]
        q3_2024 = quarters[2]
        
        # Extract net income values
        q1_income = q1_2025.get('metrics', {}).get('net_income', 0)
        q4_income = q4_2024.get('metrics', {}).get('net_income', 0)
        q3_income = q3_2024.get('metrics', {}).get('net_income', 0)
        
        print(f"\n📋 Raw Net Income Values:")
        print(f"   Q1 2025: ${q1_income/1e6:.0f}M")
        print(f"   Q4 2024: ${q4_income/1e6:.0f}M")
        print(f"   Q3 2024: ${q3_income/1e6:.0f}M")
        
        print(f"\n🧮 Growth Rate Calculations:")
        
        # Q1 vs Q4 growth rate
        if q4_income != 0:
            q1_vs_q4_growth = ((q1_income - q4_income) / abs(q4_income)) * 100
            print(f"   Q1 2025 vs Q4 2024:")
            print(f"   ({q1_income/1e6:.0f}M - ({q4_income/1e6:.0f}M)) / |{q4_income/1e6:.0f}M| × 100")
            print(f"   = ({(q1_income - q4_income)/1e6:.0f}M) / {abs(q4_income)/1e6:.0f}M × 100")
            print(f"   = {q1_vs_q4_growth:+.1f}%")
        else:
            q1_vs_q4_growth = 0
            print(f"   Q1 2025 vs Q4 2024: Cannot calculate (Q4 income is 0)")
        
        # Q4 vs Q3 growth rate  
        if q3_income != 0:
            q4_vs_q3_growth = ((q4_income - q3_income) / abs(q3_income)) * 100
            print(f"\n   Q4 2024 vs Q3 2024:")
            print(f"   ({q4_income/1e6:.0f}M - {q3_income/1e6:.0f}M) / |{q3_income/1e6:.0f}M| × 100")
            print(f"   = ({(q4_income - q3_income)/1e6:.0f}M) / {abs(q3_income)/1e6:.0f}M × 100")
            print(f"   = {q4_vs_q3_growth:+.1f}%")
        else:
            q4_vs_q3_growth = 0
            print(f"\n   Q4 2024 vs Q3 2024: Cannot calculate (Q3 income is 0)")
        
        # Average calculation
        avg_growth = (q1_vs_q4_growth + q4_vs_q3_growth) / 2
        print(f"\n📊 Average Growth Calculation:")
        print(f"   ({q1_vs_q4_growth:+.1f}% + {q4_vs_q3_growth:+.1f}%) / 2 = {avg_growth:+.1f}%")
        
        print(f"\n🎯 THE ISSUE:")
        print("=" * 30)
        
        if q4_income < 0:
            print(f"   ⚠️  Q4 2024 had a LOSS of ${abs(q4_income)/1e6:.0f}M")
            print(f"   📈 Q1 2025 had a PROFIT of ${q1_income/1e6:.0f}M")
            print(f"   🧮 Going from a loss to a profit creates extreme percentage growth")
            print(f"   📊 This is mathematically correct but misleading for trend analysis")
            
            print(f"\n💡 EXPLANATION:")
            print(f"   When a company goes from losing money to making money,")
            print(f"   the percentage calculation becomes very large because:")
            print(f"   - The denominator (previous loss) is small")
            print(f"   - The numerator (change) is large")
            print(f"   - This creates an artificially high percentage")
            
            print(f"\n🔧 BETTER ANALYSIS:")
            print(f"   Instead of focusing on the 421% figure, consider:")
            print(f"   - Q4 2024: Company had a loss (possibly one-time charges)")
            print(f"   - Q1 2025: Company returned to profitability")
            print(f"   - This suggests recovery, not 4x growth in underlying business")
        
        print(f"\n📈 CONTEXT CHECK:")
        print("=" * 30)
        
        # Check if this is a pattern or one-time event
        profitable_quarters = 0
        loss_quarters = 0
        
        for quarter in quarters[:4]:
            income = quarter.get('metrics', {}).get('net_income', 0)
            if income > 0:
                profitable_quarters += 1
            elif income < 0:
                loss_quarters += 1
        
        print(f"   Last 4 quarters: {profitable_quarters} profitable, {loss_quarters} losses")
        
        if loss_quarters == 1:
            print(f"   💡 Likely a one-time loss in Q4 2024, not a trend reversal")
        elif loss_quarters > 1:
            print(f"   ⚠️  Multiple loss quarters - more concerning pattern")
        else:
            print(f"   ✅ Consistently profitable - genuine growth")

def suggest_better_metrics():
    """Suggest better ways to analyze this situation"""
    
    print(f"\n🎯 BETTER METRICS FOR THIS SITUATION:")
    print("=" * 50)
    
    print(f"\n1. 📊 Absolute Change Analysis:")
    print(f"   - Focus on dollar amounts, not percentages")
    print(f"   - 'Returned to $646M profit from $76M loss'")
    
    print(f"\n2. 🔍 Exclude One-Time Items:")
    print(f"   - Look for special charges or gains")
    print(f"   - Focus on operating income vs net income")
    
    print(f"\n3. 📈 Longer-Term Trends:")
    print(f"   - Compare to same quarter last year")
    print(f"   - Look at 4-quarter rolling averages")
    
    print(f"\n4. 🎯 Trend Classification Improvement:")
    print(f"   - Flag 'recovery' vs 'growth' scenarios")
    print(f"   - Use different thresholds for loss-to-profit transitions")
    
    print(f"\n5. 📋 Context Indicators:")
    print(f"   - Show 'recovering from loss' instead of '421% growth'")
    print(f"   - Indicate when calculations may be misleading")

def main():
    debug_dominion_calculation()
    suggest_better_metrics()
    
    print(f"\n" + "=" * 70)
    print(f"✅ CONCLUSION:")
    print(f"=" * 70)
    print(f"\nThe 421% income trend for Dominion Energy (D) is:")
    print(f"   ✅ Mathematically correct")
    print(f"   ⚠️  But misleading for investment analysis")
    print(f"   🔍 Caused by recovery from Q4 2024 loss to Q1 2025 profit")
    print(f"   💡 Better described as 'recovery' rather than 'growth'")
    print(f"\nThis highlights a limitation in percentage-based trend analysis")
    print(f"when dealing with loss-to-profit transitions! 🎯")

if __name__ == "__main__":
    main()
