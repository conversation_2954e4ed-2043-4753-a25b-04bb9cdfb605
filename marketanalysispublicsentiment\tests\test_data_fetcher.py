"""
Unit tests for the data fetcher module.

Tests data fetching functionality including:
- RSS feed parsing
- News article extraction
- Price data retrieval
- Error handling and retries
- Data validation and formatting
"""

import pytest
import unittest
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
import sys
import os
import requests

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.data_fetcher import (
    fetch_rss_feed,
    parse_rss_articles,
    get_current_prices,
    validate_article_data,
    format_price_data
)


class TestDataFetcher(unittest.TestCase):
    """Test cases for data fetcher functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.sample_rss_xml = """<?xml version="1.0" encoding="UTF-8"?>
        <rss version="2.0">
            <channel>
                <title>Financial News</title>
                <item>
                    <title>Apple Reports Strong Q1 Earnings</title>
                    <description>Apple Inc. announced record quarterly earnings...</description>
                    <link>https://example.com/apple-earnings</link>
                    <pubDate>Wed, 15 Jan 2025 10:30:00 GMT</pubDate>
                    <source>Financial Times</source>
                </item>
                <item>
                    <title>Market Volatility Continues</title>
                    <description>Stock markets experienced significant volatility...</description>
                    <link>https://example.com/market-volatility</link>
                    <pubDate>Wed, 15 Jan 2025 09:15:00 GMT</pubDate>
                    <source>Reuters</source>
                </item>
            </channel>
        </rss>"""
        
        self.sample_article = {
            'headline': 'Apple Reports Strong Q1 Earnings',
            'text': 'Apple Inc. announced record quarterly earnings...',
            'url': 'https://example.com/apple-earnings',
            'datetime': '2025-01-15 10:30:00',
            'source': 'Financial Times',
            'ticker': 'AAPL'
        }
    
    @patch('requests.get')
    def test_fetch_rss_feed_success(self, mock_get):
        """Test successful RSS feed fetching."""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = self.sample_rss_xml
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = fetch_rss_feed('https://example.com/rss')
        
        # Should return the RSS content
        self.assertEqual(result, self.sample_rss_xml)
        mock_get.assert_called_once()
    
    @patch('requests.get')
    def test_fetch_rss_feed_http_error(self, mock_get):
        """Test RSS feed fetching with HTTP error."""
        # Mock HTTP error
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.raise_for_status.side_effect = requests.HTTPError("404 Not Found")
        mock_get.return_value = mock_response
        
        result = fetch_rss_feed('https://example.com/invalid-rss')
        
        # Should return None on error
        self.assertIsNone(result)
    
    @patch('requests.get')
    def test_fetch_rss_feed_timeout(self, mock_get):
        """Test RSS feed fetching with timeout."""
        # Mock timeout
        mock_get.side_effect = requests.Timeout("Request timed out")
        
        result = fetch_rss_feed('https://example.com/slow-rss')
        
        # Should return None on timeout
        self.assertIsNone(result)
    
    @patch('requests.get')
    def test_fetch_rss_feed_connection_error(self, mock_get):
        """Test RSS feed fetching with connection error."""
        # Mock connection error
        mock_get.side_effect = requests.ConnectionError("Connection failed")
        
        result = fetch_rss_feed('https://example.com/unreachable-rss')
        
        # Should return None on connection error
        self.assertIsNone(result)
    
    def test_parse_rss_articles_valid_xml(self):
        """Test parsing valid RSS XML."""
        articles = parse_rss_articles(self.sample_rss_xml)
        
        # Should parse 2 articles
        self.assertEqual(len(articles), 2)
        
        # Check first article
        first_article = articles[0]
        self.assertEqual(first_article['headline'], 'Apple Reports Strong Q1 Earnings')
        self.assertIn('Apple Inc. announced', first_article['text'])
        self.assertEqual(first_article['url'], 'https://example.com/apple-earnings')
        self.assertEqual(first_article['source'], 'Financial Times')
        
        # Check second article
        second_article = articles[1]
        self.assertEqual(second_article['headline'], 'Market Volatility Continues')
        self.assertEqual(second_article['source'], 'Reuters')
    
    def test_parse_rss_articles_invalid_xml(self):
        """Test parsing invalid RSS XML."""
        invalid_xml = "<invalid>xml content</invalid>"
        
        articles = parse_rss_articles(invalid_xml)
        
        # Should return empty list for invalid XML
        self.assertEqual(articles, [])
    
    def test_parse_rss_articles_empty_xml(self):
        """Test parsing empty RSS XML."""
        empty_xml = ""
        
        articles = parse_rss_articles(empty_xml)
        
        # Should return empty list for empty XML
        self.assertEqual(articles, [])
    
    def test_parse_rss_articles_malformed_items(self):
        """Test parsing RSS with malformed items."""
        malformed_xml = """<?xml version="1.0" encoding="UTF-8"?>
        <rss version="2.0">
            <channel>
                <item>
                    <title>Complete Article</title>
                    <description>Full description</description>
                    <link>https://example.com/complete</link>
                    <pubDate>Wed, 15 Jan 2025 10:30:00 GMT</pubDate>
                </item>
                <item>
                    <title>Incomplete Article</title>
                    <!-- Missing description and link -->
                </item>
            </channel>
        </rss>"""
        
        articles = parse_rss_articles(malformed_xml)
        
        # Should handle malformed items gracefully
        self.assertGreaterEqual(len(articles), 1)  # At least the complete article
        
        # Complete article should be parsed correctly
        complete_article = articles[0]
        self.assertEqual(complete_article['headline'], 'Complete Article')
        self.assertEqual(complete_article['text'], 'Full description')
    
    def test_validate_article_data_valid(self):
        """Test article data validation with valid data."""
        valid_article = self.sample_article.copy()
        
        is_valid = validate_article_data(valid_article)
        
        self.assertTrue(is_valid)
    
    def test_validate_article_data_missing_required_fields(self):
        """Test article data validation with missing required fields."""
        # Missing headline
        invalid_article1 = self.sample_article.copy()
        del invalid_article1['headline']
        
        is_valid1 = validate_article_data(invalid_article1)
        self.assertFalse(is_valid1)
        
        # Missing text
        invalid_article2 = self.sample_article.copy()
        del invalid_article2['text']
        
        is_valid2 = validate_article_data(invalid_article2)
        self.assertFalse(is_valid2)
        
        # Missing URL
        invalid_article3 = self.sample_article.copy()
        del invalid_article3['url']
        
        is_valid3 = validate_article_data(invalid_article3)
        self.assertFalse(is_valid3)
    
    def test_validate_article_data_empty_fields(self):
        """Test article data validation with empty fields."""
        # Empty headline
        invalid_article1 = self.sample_article.copy()
        invalid_article1['headline'] = ''
        
        is_valid1 = validate_article_data(invalid_article1)
        self.assertFalse(is_valid1)
        
        # Empty text
        invalid_article2 = self.sample_article.copy()
        invalid_article2['text'] = '   '  # Whitespace only
        
        is_valid2 = validate_article_data(invalid_article2)
        self.assertFalse(is_valid2)
    
    def test_validate_article_data_invalid_url(self):
        """Test article data validation with invalid URL."""
        invalid_article = self.sample_article.copy()
        invalid_article['url'] = 'not-a-valid-url'
        
        is_valid = validate_article_data(invalid_article)
        self.assertFalse(is_valid)
    
    @patch('src.data.data_fetcher.yf.download')
    def test_get_current_prices_success(self, mock_download):
        """Test successful price data retrieval."""
        # Mock yfinance response
        mock_data = Mock()
        mock_data.iloc = Mock()
        mock_data.iloc.__getitem__.return_value = {
            'Close': 196.45,
            'Volume': 45234567
        }
        mock_download.return_value = mock_data
        
        tickers = ['AAPL', 'MSFT']
        prices = get_current_prices(tickers)
        
        # Should return price data for requested tickers
        self.assertIn('AAPL', prices)
        self.assertIn('MSFT', prices)
        
        # Check price data structure
        aapl_data = prices['AAPL']
        self.assertIn('current_price', aapl_data)
        self.assertIn('volume', aapl_data)
    
    @patch('src.data.data_fetcher.yf.download')
    def test_get_current_prices_failure(self, mock_download):
        """Test price data retrieval failure."""
        # Mock yfinance failure
        mock_download.side_effect = Exception("API Error")
        
        tickers = ['INVALID_TICKER']
        prices = get_current_prices(tickers)
        
        # Should return empty dict on failure
        self.assertEqual(prices, {})
    
    def test_format_price_data(self):
        """Test price data formatting."""
        raw_price_data = {
            'AAPL': {
                'Close': 196.45,
                'Volume': 45234567,
                'High': 198.50,
                'Low': 195.20
            }
        }
        
        formatted = format_price_data(raw_price_data)
        
        # Check formatting
        self.assertIn('AAPL', formatted)
        aapl_data = formatted['AAPL']
        
        self.assertEqual(aapl_data['current_price'], 196.45)
        self.assertEqual(aapl_data['volume'], 45234567)
        self.assertEqual(aapl_data['high'], 198.50)
        self.assertEqual(aapl_data['low'], 195.20)
    
    def test_format_price_data_missing_fields(self):
        """Test price data formatting with missing fields."""
        incomplete_data = {
            'AAPL': {
                'Close': 196.45
                # Missing Volume, High, Low
            }
        }
        
        formatted = format_price_data(incomplete_data)
        
        # Should handle missing fields gracefully
        self.assertIn('AAPL', formatted)
        aapl_data = formatted['AAPL']
        
        self.assertEqual(aapl_data['current_price'], 196.45)
        self.assertEqual(aapl_data['volume'], 0)  # Default value
        self.assertEqual(aapl_data['high'], 196.45)  # Default to close
        self.assertEqual(aapl_data['low'], 196.45)  # Default to close


class TestDataFetcherIntegration(unittest.TestCase):
    """Integration tests for data fetcher with external dependencies."""
    
    @patch('src.data.data_fetcher.cache_manager')
    @patch('requests.get')
    def test_rss_fetching_with_cache(self, mock_get, mock_cache):
        """Test RSS fetching integration with cache."""
        # Mock cache miss
        mock_cache.get.return_value = None
        
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<rss>test</rss>"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = fetch_rss_feed('https://example.com/rss')
        
        # Should attempt to cache the result
        mock_cache.set.assert_called()
    
    def test_article_processing_pipeline(self):
        """Test the complete article processing pipeline."""
        # This would test the integration of:
        # fetch_rss_feed -> parse_rss_articles -> validate_article_data
        
        # Mock the RSS fetching
        with patch('src.data.data_fetcher.fetch_rss_feed') as mock_fetch:
            mock_fetch.return_value = self.sample_rss_xml
            
            # Test the pipeline
            rss_content = fetch_rss_feed('https://example.com/rss')
            articles = parse_rss_articles(rss_content)
            
            # Validate all articles
            valid_articles = [article for article in articles if validate_article_data(article)]
            
            # Should have valid articles
            self.assertGreater(len(valid_articles), 0)
            
            # All articles should be valid
            self.assertEqual(len(valid_articles), len(articles))


if __name__ == '__main__':
    unittest.main()
