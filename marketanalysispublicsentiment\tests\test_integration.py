"""
Integration tests for the Financial Sentiment Analyzer.

Tests end-to-end workflows and component integration including:
- Complete analysis pipeline
- Data flow between components
- Cache integration across modules
- Error handling in complex scenarios
- Real-world usage patterns
"""

import pytest
import unittest
from unittest.mock import patch, Mo<PERSON>, MagicMock
import sys
import os
import tempfile
import shutil

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.financial_analyzer import fetch_all_data, analyze_all_data
from src.core.sentiment_analyzer import analyze_multi_ticker_sentiment
from src.core.policy_analyzer import analyze_policy_sentiment
from src.data.cache_manager import CacheManager


class TestIntegration(unittest.TestCase):
    """Integration test cases."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        # Create temporary cache directory
        self.temp_cache_dir = tempfile.mkdtemp()
        
        # Sample realistic data
        self.realistic_news_data = [
            {
                'headline': 'Apple Reports Record Q1 Earnings, Beats Expectations',
                'text': 'Apple Inc. announced outstanding quarterly earnings with revenue of $123.9 billion, exceeding analyst expectations.',
                'ticker': 'AAPL',
                'url': 'https://example.com/apple-earnings',
                'datetime': '2025-01-15 16:30:00',
                'source': 'Reuters'
            },
            {
                'headline': 'Tesla Stock Surges on Strong Delivery Numbers',
                'text': 'Tesla delivered a record number of vehicles in Q4, driving stock price higher.',
                'ticker': 'TSLA',
                'url': 'https://example.com/tesla-deliveries',
                'datetime': '2025-01-15 15:45:00',
                'source': 'Bloomberg'
            },
            {
                'headline': 'Microsoft Azure Growth Continues to Accelerate',
                'text': 'Microsoft reported excellent cloud revenue growth with Azure leading the way.',
                'ticker': 'MSFT',
                'url': 'https://example.com/microsoft-azure',
                'datetime': '2025-01-15 14:20:00',
                'source': 'Financial Times'
            },
            {
                'headline': 'Market Volatility Increases Amid Economic Uncertainty',
                'text': 'Stock markets experienced significant volatility as investors react to mixed economic signals.',
                'ticker': 'SPY',
                'url': 'https://example.com/market-volatility',
                'datetime': '2025-01-15 13:10:00',
                'source': 'CNBC'
            }
        ]
        
        self.realistic_government_data = [
            {
                'headline': 'Federal Reserve Keeps Interest Rates Unchanged',
                'text': 'The Federal Open Market Committee voted to maintain the current federal funds rate at 5.25-5.50%.',
                'url': 'https://federalreserve.gov/newsevents',
                'category': 'monetary',
                'source': 'Federal Reserve'
            },
            {
                'headline': 'Treasury Announces New Infrastructure Investment',
                'text': 'The Treasury Department unveiled a $50 billion infrastructure investment program.',
                'url': 'https://treasury.gov/news',
                'category': 'fiscal',
                'source': 'Treasury Department'
            }
        ]
        
        self.realistic_market_data = {
            'SPY': {'name': 'S&P 500', 'price_change': 1.2},
            'QQQ': {'name': 'NASDAQ 100', 'price_change': 1.8},
            'DIA': {'name': 'Dow Jones', 'price_change': 0.5},
            'IWM': {'name': 'Russell 2000', 'price_change': -0.3}
        }
    
    def tearDown(self):
        """Clean up after tests."""
        shutil.rmtree(self.temp_cache_dir, ignore_errors=True)
    
    @patch('src.core.financial_analyzer.fetch_market_news_parallel')
    @patch('src.core.financial_analyzer.fetch_government_news_parallel')
    @patch('src.core.financial_analyzer.get_market_data_optimized')
    def test_complete_analysis_pipeline(self, mock_market_data, mock_gov_news, mock_market_news):
        """Test the complete end-to-end analysis pipeline."""
        # Mock data sources
        mock_market_news.return_value = (self.realistic_news_data, {'total': 4, 'cached': 0})
        mock_gov_news.return_value = (self.realistic_government_data, {'total': 2, 'cached': 0})
        mock_market_data.return_value = self.realistic_market_data
        
        # Step 1: Fetch all data
        fetch_result = fetch_all_data(quick_mode=False)
        self.assertEqual(len(fetch_result), 6)
        
        news_data, news_stats, government_data, policy_stats, market_data, market_historical_data = fetch_result
        
        # Verify data was fetched correctly
        self.assertEqual(len(news_data), 4)
        self.assertEqual(len(government_data), 2)
        self.assertEqual(len(market_data), 4)
        
        # Step 2: Analyze all data
        analysis_result = analyze_all_data(news_data, government_data, market_data)
        self.assertEqual(len(analysis_result), 13)
        
        (sentiment_analysis, policy_analysis, market_health, sector_rankings,
         ticker_rankings, price_changes, current_prices, company_names, sentiment_scores,
         sentiment_details, multi_ticker_articles, cross_ticker_analysis, earnings_data) = analysis_result
        
        # Verify analysis results
        self.assertIsInstance(sentiment_analysis, dict)
        self.assertIsInstance(policy_analysis, dict)
        self.assertIsInstance(market_health, dict)
        self.assertIsInstance(ticker_rankings, list)
        
        # Verify sentiment analysis
        self.assertIn('market_mood', sentiment_analysis)
        self.assertIn('average_sentiment', sentiment_analysis)
        
        # Verify policy analysis
        self.assertIn('policy_sentiment', policy_analysis)
        self.assertIn('policy_mood', policy_analysis)
        
        # Verify market health
        self.assertIn('recommendation', market_health)
        
        print(f"✅ Complete pipeline test passed")
        print(f"   - Processed {len(news_data)} news articles")
        print(f"   - Processed {len(government_data)} policy articles")
        print(f"   - Analyzed {len(market_data)} market indices")
        print(f"   - Market mood: {sentiment_analysis.get('market_mood', 'Unknown')}")
        print(f"   - Policy mood: {policy_analysis.get('policy_mood', 'Unknown')}")
        print(f"   - Recommendation: {market_health.get('recommendation', 'Unknown')}")
    
    def test_sentiment_and_policy_integration(self):
        """Test integration between sentiment and policy analysis."""
        # Analyze sentiment
        sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(
            self.realistic_news_data
        )
        
        # Analyze policy
        policy_result = analyze_policy_sentiment(self.realistic_government_data)
        
        # Verify both analyses completed
        self.assertEqual(len(sentiment_scores), 4)
        self.assertEqual(len(sentiment_details), 4)
        self.assertIsInstance(policy_result, dict)
        
        # Verify sentiment analysis found tickers
        tickers_found = set()
        for detail in sentiment_details:
            if 'ticker' in detail:
                tickers_found.add(detail['ticker'])
        
        self.assertGreater(len(tickers_found), 0)
        
        # Verify policy analysis processed articles
        self.assertEqual(policy_result['total_policy_articles'], 2)
        self.assertIn('policy_sentiment', policy_result)
        
        print(f"✅ Sentiment-Policy integration test passed")
        print(f"   - Found {len(tickers_found)} unique tickers")
        print(f"   - Policy sentiment: {policy_result['policy_sentiment']:.3f}")
    
    def test_cache_integration_across_modules(self):
        """Test cache integration across different modules."""
        # Create cache manager with temp directory
        cache_manager = CacheManager(cache_dir=self.temp_cache_dir)
        
        # Test caching different types of data
        test_data_types = [
            ('news', 'AAPL_latest', self.realistic_news_data[0]),
            ('prices', 'AAPL_current', {'price': 196.45, 'change': -2.75}),
            ('earnings', 'AAPL_quarterly', {'revenue': 123.9, 'net_income': 34.6}),
            ('policy', 'fed_latest', self.realistic_government_data[0])
        ]
        
        # Cache all data types
        for data_type, identifier, data in test_data_types:
            cache_manager.cache_data(data_type, identifier, data)
        
        # Verify all data can be retrieved
        for data_type, identifier, original_data in test_data_types:
            cached_data = cache_manager.get_cached_data(data_type, identifier)
            self.assertEqual(cached_data, original_data)
        
        # Test cache statistics
        stats = cache_manager.get_cache_stats()
        self.assertGreaterEqual(stats['cache_files'], 4)
        self.assertGreater(stats['cache_size_mb'], 0)
        
        print(f"✅ Cache integration test passed")
        print(f"   - Cached {len(test_data_types)} different data types")
        print(f"   - Cache files: {stats['cache_files']}")
        print(f"   - Cache size: {stats['cache_size_mb']:.3f} MB")
    
    @patch('src.core.financial_analyzer.fetch_market_news_parallel')
    @patch('src.core.financial_analyzer.fetch_government_news_parallel')
    @patch('src.core.financial_analyzer.get_market_data_optimized')
    def test_error_handling_integration(self, mock_market_data, mock_gov_news, mock_market_news):
        """Test error handling across integrated components."""
        # Test with one component failing
        mock_market_news.return_value = (self.realistic_news_data, {'total': 4})
        mock_gov_news.side_effect = Exception("Government API Error")
        mock_market_data.return_value = self.realistic_market_data
        
        # Should handle the error gracefully
        try:
            fetch_result = fetch_all_data()
            # If no exception, verify partial data
            news_data, news_stats, government_data, policy_stats, market_data, _ = fetch_result
            
            # News and market data should still work
            self.assertEqual(len(news_data), 4)
            self.assertEqual(len(market_data), 4)
            
            # Government data might be empty due to error
            self.assertIsInstance(government_data, list)
            
        except Exception as e:
            # If exception is raised, it should be handled appropriately
            self.fail(f"Error handling failed: {e}")
        
        print("✅ Error handling integration test passed")
    
    def test_data_flow_consistency(self):
        """Test data flow consistency between components."""
        # Process news through sentiment analysis
        sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(
            self.realistic_news_data
        )
        
        # Verify data consistency
        self.assertEqual(len(sentiment_scores), len(self.realistic_news_data))
        self.assertEqual(len(sentiment_details), len(self.realistic_news_data))
        
        # Check that sentiment details contain original article information
        for i, detail in enumerate(sentiment_details):
            original_article = self.realistic_news_data[i]
            self.assertEqual(detail['headline'], original_article['headline'])
        
        # Verify multi-ticker articles are subset of original
        for multi_article in multi_ticker_articles:
            self.assertIn(multi_article, self.realistic_news_data)
        
        print(f"✅ Data flow consistency test passed")
        print(f"   - Processed {len(sentiment_scores)} articles")
        print(f"   - Found {len(multi_ticker_articles)} multi-ticker articles")
    
    def test_performance_integration(self):
        """Test performance of integrated components."""
        import time
        
        # Create larger dataset for performance testing
        large_news_data = self.realistic_news_data * 25  # 100 articles
        large_gov_data = self.realistic_government_data * 10  # 20 articles
        
        start_time = time.time()
        
        # Run sentiment analysis
        sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(
            large_news_data
        )
        
        # Run policy analysis
        policy_result = analyze_policy_sentiment(large_gov_data)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should complete in reasonable time
        self.assertLess(processing_time, 30.0)  # 30 seconds max
        
        # Verify all data was processed
        self.assertEqual(len(sentiment_scores), 100)
        self.assertEqual(policy_result['total_policy_articles'], 20)
        
        articles_per_second = 120 / processing_time  # 100 + 20 articles
        
        print(f"✅ Performance integration test passed")
        print(f"   - Processing time: {processing_time:.3f} seconds")
        print(f"   - Rate: {articles_per_second:.1f} articles/second")
    
    def test_real_world_scenario_simulation(self):
        """Test a realistic end-to-end scenario."""
        # Simulate a typical user workflow
        
        # 1. User starts application and fetches data
        with patch('src.core.financial_analyzer.fetch_market_news_parallel') as mock_news, \
             patch('src.core.financial_analyzer.fetch_government_news_parallel') as mock_gov, \
             patch('src.core.financial_analyzer.get_market_data_optimized') as mock_market:
            
            mock_news.return_value = (self.realistic_news_data, {'total': 4, 'cached': 2})
            mock_gov.return_value = (self.realistic_government_data, {'total': 2, 'cached': 0})
            mock_market.return_value = self.realistic_market_data
            
            # Fetch data
            fetch_result = fetch_all_data(quick_mode=True)
            news_data, news_stats, government_data, policy_stats, market_data, _ = fetch_result
            
            # 2. User analyzes the data
            analysis_result = analyze_all_data(news_data, government_data, market_data)
            sentiment_analysis, policy_analysis, market_health = analysis_result[:3]
            
            # 3. Verify realistic results
            self.assertIn(sentiment_analysis['market_mood'], 
                         ['Very Positive', 'Positive', 'Neutral', 'Negative', 'Very Negative'])
            
            self.assertIn(policy_analysis['policy_mood'], 
                         ['Market Supportive', 'Positive', 'Neutral', 'Negative', 'Market Negative', 'No Policy Data'])
            
            self.assertIn(market_health['recommendation'], 
                         ['STRONG BUY', 'BUY', 'CAUTIOUSLY OPTIMISTIC', 'HOLD', 'CAUTIOUS', 'SELL', 'INSUFFICIENT DATA'])
            
            # 4. Verify cache was used (some articles cached)
            self.assertEqual(news_stats['cached'], 2)
            
            print(f"✅ Real-world scenario test passed")
            print(f"   - Market mood: {sentiment_analysis['market_mood']}")
            print(f"   - Policy mood: {policy_analysis['policy_mood']}")
            print(f"   - Recommendation: {market_health['recommendation']}")
            print(f"   - Cache efficiency: {news_stats['cached']}/{news_stats['total']} articles cached")


class TestIntegrationEdgeCases(unittest.TestCase):
    """Test edge cases in integration scenarios."""
    
    def test_empty_data_integration(self):
        """Test integration with empty datasets."""
        # Test with completely empty data
        analysis_result = analyze_all_data([], [], {})
        
        # Should handle gracefully
        self.assertEqual(len(analysis_result), 13)
        
        sentiment_analysis, policy_analysis, market_health = analysis_result[:3]
        
        # Should have default values
        self.assertIsInstance(sentiment_analysis, dict)
        self.assertIsInstance(policy_analysis, dict)
        self.assertIsInstance(market_health, dict)
        
        print("✅ Empty data integration test passed")
    
    def test_mixed_quality_data_integration(self):
        """Test integration with mixed quality data."""
        # Create data with some invalid entries
        mixed_news_data = [
            {
                'headline': 'Valid Article',
                'text': 'This is a valid article with proper content.',
                'ticker': 'AAPL'
            },
            {
                'headline': '',  # Empty headline
                'text': 'Article with empty headline',
                'ticker': 'MSFT'
            },
            {
                'headline': 'Article without text',
                'ticker': 'GOOGL'
                # Missing text field
            }
        ]
        
        # Should handle mixed quality data
        sentiment_scores, sentiment_details, _ = analyze_multi_ticker_sentiment(mixed_news_data)
        
        # Should process all articles (even invalid ones)
        self.assertEqual(len(sentiment_scores), 3)
        self.assertEqual(len(sentiment_details), 3)
        
        print("✅ Mixed quality data integration test passed")


if __name__ == '__main__':
    unittest.main()
