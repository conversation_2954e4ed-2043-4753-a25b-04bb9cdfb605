"""
Unit tests for the earnings fetcher module.

Tests earnings data fetching and analysis including:
- Earnings data retrieval
- Trend calculation (revenue and income trends)
- Quarterly data processing
- Error handling for missing data
"""

import pytest
import unittest
from unittest.mock import patch, Mock
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.earnings_fetcher import (
    get_earnings_summary_for_ticker,
    calculate_earnings_trends,
    format_earnings_display,
    get_quarterly_data
)


class TestEarningsFetcher(unittest.TestCase):
    """Test cases for earnings fetcher functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.sample_earnings_data = {
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 155.7, 'net_income': 17.1},
                {'quarter': 'Q4 2024', 'revenue': 154.9, 'net_income': 15.7},
                {'quarter': 'Q3 2024', 'revenue': 147.8, 'net_income': 14.2},
                {'quarter': 'Q2 2024', 'revenue': 143.1, 'net_income': 13.8}
            ]
        }
        
        self.declining_earnings_data = {
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 120.0, 'net_income': 10.0},
                {'quarter': 'Q4 2024', 'revenue': 130.0, 'net_income': 12.0},
                {'quarter': 'Q3 2024', 'revenue': 140.0, 'net_income': 14.0},
                {'quarter': 'Q2 2024', 'revenue': 145.0, 'net_income': 15.0}
            ]
        }
    
    def test_calculate_earnings_trends_improving(self):
        """Test earnings trend calculation for improving company."""
        trends = calculate_earnings_trends(self.sample_earnings_data['quarters'])
        
        # Check that trends are calculated
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
        self.assertIn('revenue_growth_rate', trends)
        self.assertIn('income_growth_rate', trends)
        
        # Revenue should be improving (positive growth)
        self.assertGreater(trends['revenue_growth_rate'], 0)
        self.assertEqual(trends['revenue_trend'], 'Improving')
        
        # Income should be improving (positive growth)
        self.assertGreater(trends['income_growth_rate'], 0)
        self.assertEqual(trends['income_trend'], 'Improving')
    
    def test_calculate_earnings_trends_declining(self):
        """Test earnings trend calculation for declining company."""
        trends = calculate_earnings_trends(self.declining_earnings_data['quarters'])
        
        # Revenue should be declining (negative growth)
        self.assertLess(trends['revenue_growth_rate'], 0)
        self.assertEqual(trends['revenue_trend'], 'Declining')
        
        # Income should be declining (negative growth)
        self.assertLess(trends['income_growth_rate'], 0)
        self.assertEqual(trends['income_trend'], 'Declining')
    
    def test_calculate_earnings_trends_stable(self):
        """Test earnings trend calculation for stable company."""
        stable_data = {
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 100.0, 'net_income': 10.0},
                {'quarter': 'Q4 2024', 'revenue': 101.0, 'net_income': 10.1},
                {'quarter': 'Q3 2024', 'revenue': 99.0, 'net_income': 9.9},
                {'quarter': 'Q2 2024', 'revenue': 100.5, 'net_income': 10.0}
            ]
        }
        
        trends = calculate_earnings_trends(stable_data['quarters'])
        
        # Growth rates should be small (within stable range)
        self.assertLess(abs(trends['revenue_growth_rate']), 5)
        self.assertLess(abs(trends['income_growth_rate']), 5)
        
        # Trends should be stable
        self.assertEqual(trends['revenue_trend'], 'Stable')
        self.assertEqual(trends['income_trend'], 'Stable')
    
    def test_calculate_earnings_trends_insufficient_data(self):
        """Test earnings trend calculation with insufficient data."""
        insufficient_data = [
            {'quarter': 'Q1 2025', 'revenue': 100.0, 'net_income': 10.0}
        ]
        
        trends = calculate_earnings_trends(insufficient_data)
        
        # Should handle gracefully with default values
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
        self.assertEqual(trends['revenue_trend'], 'Stable')
        self.assertEqual(trends['income_trend'], 'Stable')
    
    def test_calculate_earnings_trends_missing_fields(self):
        """Test earnings trend calculation with missing data fields."""
        incomplete_data = [
            {'quarter': 'Q1 2025', 'revenue': 100.0},  # Missing net_income
            {'quarter': 'Q4 2024', 'net_income': 10.0}  # Missing revenue
        ]
        
        trends = calculate_earnings_trends(incomplete_data)
        
        # Should handle gracefully
        self.assertIsInstance(trends, dict)
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
    
    def test_format_earnings_display(self):
        """Test earnings data formatting for display."""
        earnings_summary = {
            'revenue_trend': 'Improving',
            'income_trend': 'Improving',
            'revenue_growth_rate': 8.5,
            'income_growth_rate': 12.3,
            'latest_quarter': 'Q1 2025',
            'latest_revenue': 155.7,
            'latest_income': 17.1
        }
        
        display_text = format_earnings_display(earnings_summary)
        
        # Check that display contains key information
        self.assertIn('Improving', display_text)
        self.assertIn('8.5%', display_text)
        self.assertIn('12.3%', display_text)
        self.assertIn('Q1 2025', display_text)
    
    @patch('src.core.earnings_fetcher.yf.Ticker')
    def test_get_earnings_summary_for_ticker_success(self, mock_ticker):
        """Test successful earnings data retrieval for a ticker."""
        # Mock yfinance response
        mock_instance = Mock()
        mock_instance.quarterly_financials = Mock()
        mock_instance.quarterly_financials.loc = Mock()
        
        # Mock the data structure that yfinance returns
        mock_revenue_data = Mock()
        mock_revenue_data.dropna.return_value = [155.7e9, 154.9e9, 147.8e9, 143.1e9]
        
        mock_income_data = Mock()
        mock_income_data.dropna.return_value = [17.1e9, 15.7e9, 14.2e9, 13.8e9]
        
        mock_instance.quarterly_financials.loc.__getitem__.side_effect = lambda x: {
            'Total Revenue': mock_revenue_data,
            'Net Income': mock_income_data
        }[x]
        
        mock_ticker.return_value = mock_instance
        
        result = get_earnings_summary_for_ticker('AAPL')
        
        # Check that result contains expected fields
        self.assertIn('revenue_trend', result)
        self.assertIn('income_trend', result)
        self.assertIn('quarters_data', result)
    
    @patch('src.core.earnings_fetcher.yf.Ticker')
    def test_get_earnings_summary_for_ticker_failure(self, mock_ticker):
        """Test earnings data retrieval failure handling."""
        # Mock yfinance to raise an exception
        mock_ticker.side_effect = Exception("API Error")
        
        result = get_earnings_summary_for_ticker('INVALID_TICKER')
        
        # Should return default/empty result
        self.assertIsInstance(result, dict)
        self.assertIn('error', result)
    
    def test_get_quarterly_data_processing(self):
        """Test quarterly data processing and formatting."""
        raw_data = {
            'revenue': [155.7e9, 154.9e9, 147.8e9, 143.1e9],
            'income': [17.1e9, 15.7e9, 14.2e9, 13.8e9],
            'quarters': ['Q1 2025', 'Q4 2024', 'Q3 2024', 'Q2 2024']
        }
        
        processed = get_quarterly_data(raw_data)
        
        # Check that data is properly formatted
        self.assertIsInstance(processed, list)
        self.assertEqual(len(processed), 4)
        
        # Check first quarter data
        first_quarter = processed[0]
        self.assertEqual(first_quarter['quarter'], 'Q1 2025')
        self.assertAlmostEqual(first_quarter['revenue'], 155.7, places=1)
        self.assertAlmostEqual(first_quarter['net_income'], 17.1, places=1)
    
    def test_earnings_trend_boundary_conditions(self):
        """Test earnings trend calculation at boundary conditions."""
        # Test exactly at 5% threshold
        boundary_data = [
            {'quarter': 'Q1 2025', 'revenue': 105.0, 'net_income': 10.5},
            {'quarter': 'Q4 2024', 'revenue': 100.0, 'net_income': 10.0},
            {'quarter': 'Q3 2024', 'revenue': 100.0, 'net_income': 10.0}
        ]
        
        trends = calculate_earnings_trends(boundary_data)
        
        # At exactly 5% growth, should be classified as improving
        self.assertEqual(trends['revenue_trend'], 'Improving')
        self.assertEqual(trends['income_trend'], 'Improving')
    
    def test_earnings_data_validation(self):
        """Test validation of earnings data inputs."""
        # Test with None values
        invalid_data = [
            {'quarter': 'Q1 2025', 'revenue': None, 'net_income': 10.0},
            {'quarter': 'Q4 2024', 'revenue': 100.0, 'net_income': None}
        ]
        
        trends = calculate_earnings_trends(invalid_data)
        
        # Should handle None values gracefully
        self.assertIsInstance(trends, dict)
        
        # Test with negative values
        negative_data = [
            {'quarter': 'Q1 2025', 'revenue': -50.0, 'net_income': -5.0},
            {'quarter': 'Q4 2024', 'revenue': 100.0, 'net_income': 10.0}
        ]
        
        trends = calculate_earnings_trends(negative_data)
        
        # Should handle negative values
        self.assertIsInstance(trends, dict)


class TestEarningsFetcherIntegration(unittest.TestCase):
    """Integration tests for earnings fetcher with mocked external dependencies."""
    
    @patch('src.core.earnings_fetcher.cache_manager')
    @patch('src.core.earnings_fetcher.yf.Ticker')
    def test_earnings_fetcher_with_cache(self, mock_ticker, mock_cache):
        """Test earnings fetcher integration with cache."""
        # Mock cache miss
        mock_cache.get.return_value = None
        
        # Mock yfinance response
        mock_instance = Mock()
        mock_ticker.return_value = mock_instance
        
        result = get_earnings_summary_for_ticker('AAPL')
        
        # Should attempt to cache the result
        mock_cache.set.assert_called()


if __name__ == '__main__':
    unittest.main()
