"""
Unit tests for the cache manager module.

Tests caching functionality including:
- Cache storage and retrieval
- TTL (Time To Live) expiration
- Cache key generation
- Cache statistics and monitoring
- Cache cleanup operations
"""

import pytest
import unittest
from unittest.mock import patch, Mock, mock_open
import sys
import os
import tempfile
import shutil
import time
import json
from datetime import datetime, timedelta

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.cache_manager import CacheManager, cache_manager


class TestCacheManager(unittest.TestCase):
    """Test cases for cache manager functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary directory for cache testing
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = CacheManager(cache_dir=self.temp_dir)
        
        # Sample data for testing
        self.test_data = {
            'ticker': 'AAPL',
            'price': 196.45,
            'timestamp': '2025-01-15 10:30:00'
        }
        
        self.test_key = 'test_prices_AAPL_current'
    
    def tearDown(self):
        """Clean up after each test method."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_data_and_get(self):
        """Test basic cache data and get operations."""
        # Cache data
        self.cache_manager.cache_data('test_type', 'test_id', self.test_data)

        # Retrieve data from cache
        retrieved_data = self.cache_manager.get_cached_data('test_type', 'test_id')
        self.assertEqual(retrieved_data, self.test_data)
    
    def test_cache_get_nonexistent_key(self):
        """Test getting data for a key that doesn't exist."""
        result = self.cache_manager.get_cached_data('nonexistent_type', 'nonexistent_id')
        self.assertIsNone(result)
    
    def test_cache_ttl_expiration(self):
        """Test that cache entries expire after TTL."""
        # Set data with very short TTL
        self.cache_manager.set(self.test_key, self.test_data, ttl_minutes=0.01)  # 0.6 seconds
        
        # Should be available immediately
        result = self.cache_manager.get(self.test_key)
        self.assertEqual(result, self.test_data)
        
        # Wait for expiration
        time.sleep(1)
        
        # Should be expired now
        result = self.cache_manager.get(self.test_key)
        self.assertIsNone(result)
    
    def test_cache_is_expired(self):
        """Test the is_expired method."""
        # Set data with short TTL
        self.cache_manager.set(self.test_key, self.test_data, ttl_minutes=0.01)
        
        # Should not be expired immediately
        self.assertFalse(self.cache_manager.is_expired(self.test_key))
        
        # Wait for expiration
        time.sleep(1)
        
        # Should be expired now
        self.assertTrue(self.cache_manager.is_expired(self.test_key))
    
    def test_cache_is_expired_nonexistent_key(self):
        """Test is_expired for a key that doesn't exist."""
        result = self.cache_manager.is_expired('nonexistent_key')
        self.assertTrue(result)  # Non-existent keys are considered expired
    
    def test_cache_key_generation(self):
        """Test cache key generation for different data types."""
        # Test with different key types
        key1 = self.cache_manager._generate_cache_key('prices', 'AAPL', 'current')
        key2 = self.cache_manager._generate_cache_key('news', 'TSLA')
        key3 = self.cache_manager._generate_cache_key('earnings', 'MSFT', 'quarterly')
        
        # Keys should be strings and different
        self.assertIsInstance(key1, str)
        self.assertIsInstance(key2, str)
        self.assertIsInstance(key3, str)
        self.assertNotEqual(key1, key2)
        self.assertNotEqual(key2, key3)
        
        # Keys should be consistent for same inputs
        key1_repeat = self.cache_manager._generate_cache_key('prices', 'AAPL', 'current')
        self.assertEqual(key1, key1_repeat)
    
    def test_cache_file_operations(self):
        """Test cache file creation and management."""
        # Set data
        self.cache_manager.set(self.test_key, self.test_data, ttl_minutes=60)
        
        # Check that cache file was created
        cache_files = os.listdir(self.temp_dir)
        self.assertGreater(len(cache_files), 0)
        
        # Check that cache file contains expected data
        cache_file_path = os.path.join(self.temp_dir, cache_files[0])
        self.assertTrue(os.path.exists(cache_file_path))
        
        with open(cache_file_path, 'r') as f:
            cached_content = json.load(f)
            self.assertIn('data', cached_content)
            self.assertIn('timestamp', cached_content)
            self.assertIn('ttl_minutes', cached_content)
    
    def test_cache_cleanup(self):
        """Test cache cleanup of expired entries."""
        # Set multiple entries with different TTLs
        self.cache_manager.set('key1', {'data': 'test1'}, ttl_minutes=60)  # Long TTL
        self.cache_manager.set('key2', {'data': 'test2'}, ttl_minutes=0.01)  # Short TTL
        
        # Wait for short TTL to expire
        time.sleep(1)
        
        # Cleanup expired entries
        cleaned_count = self.cache_manager.cleanup_expired()
        
        # Should have cleaned up at least one entry
        self.assertGreaterEqual(cleaned_count, 1)
        
        # Long TTL entry should still exist
        result1 = self.cache_manager.get('key1')
        self.assertIsNotNone(result1)
        
        # Short TTL entry should be gone
        result2 = self.cache_manager.get('key2')
        self.assertIsNone(result2)
    
    def test_cache_statistics(self):
        """Test cache statistics collection."""
        # Add some data to cache
        self.cache_manager.set('key1', {'data': 'test1'}, ttl_minutes=60)
        self.cache_manager.set('key2', {'data': 'test2'}, ttl_minutes=60)
        
        # Get statistics
        stats = self.cache_manager.get_cache_stats()
        
        # Check statistics structure
        self.assertIn('total_files', stats)
        self.assertIn('total_size_mb', stats)
        self.assertIn('oldest_file', stats)
        self.assertIn('newest_file', stats)
        
        # Check that we have files
        self.assertGreaterEqual(stats['total_files'], 2)
        self.assertGreater(stats['total_size_mb'], 0)
    
    def test_cache_request_logging(self):
        """Test cache request logging functionality."""
        # Make some cache requests
        self.cache_manager.log_request('prices_AAPL_current')
        self.cache_manager.log_request('news_TSLA')
        self.cache_manager.log_request('prices_AAPL_current')  # Duplicate
        
        # Get request statistics
        stats = self.cache_manager.get_request_stats()
        
        # Check that requests were logged
        self.assertIn('prices_AAPL_current', stats)
        self.assertIn('news_TSLA', stats)
        
        # Check request counts
        self.assertEqual(stats['prices_AAPL_current'], 2)
        self.assertEqual(stats['news_TSLA'], 1)
    
    def test_cache_error_handling(self):
        """Test cache error handling for various failure scenarios."""
        # Test with invalid cache directory
        invalid_cache = CacheManager(cache_dir='/invalid/path/that/does/not/exist')
        
        # Should handle gracefully
        result = invalid_cache.set('test_key', {'data': 'test'})
        self.assertFalse(result)
        
        result = invalid_cache.get('test_key')
        self.assertIsNone(result)
    
    @patch('builtins.open', side_effect=PermissionError("Permission denied"))
    def test_cache_permission_error(self, mock_open):
        """Test cache handling when file permissions are denied."""
        result = self.cache_manager.set(self.test_key, self.test_data)
        self.assertFalse(result)
    
    @patch('json.load', side_effect=json.JSONDecodeError("Invalid JSON", "", 0))
    def test_cache_corrupted_file_handling(self, mock_json_load):
        """Test handling of corrupted cache files."""
        # Create a corrupted cache file
        cache_file = os.path.join(self.temp_dir, 'corrupted.cache')
        with open(cache_file, 'w') as f:
            f.write('invalid json content')
        
        # Should handle gracefully
        result = self.cache_manager.get('corrupted_key')
        self.assertIsNone(result)
    
    def test_cache_large_data(self):
        """Test caching of large data objects."""
        # Create large test data
        large_data = {
            'articles': [{'headline': f'Article {i}', 'content': 'x' * 1000} for i in range(100)]
        }
        
        # Should handle large data
        success = self.cache_manager.set('large_data_key', large_data, ttl_minutes=60)
        self.assertTrue(success)
        
        # Should retrieve large data correctly
        retrieved = self.cache_manager.get('large_data_key')
        self.assertEqual(len(retrieved['articles']), 100)
        self.assertEqual(retrieved['articles'][0]['headline'], 'Article 0')
    
    def test_cache_unicode_data(self):
        """Test caching of unicode and special character data."""
        unicode_data = {
            'company': 'Café Münchën',
            'description': '这是一个测试 🚀📊💰',
            'symbols': '€£¥₹'
        }
        
        # Should handle unicode data
        success = self.cache_manager.set('unicode_key', unicode_data, ttl_minutes=60)
        self.assertTrue(success)
        
        # Should retrieve unicode data correctly
        retrieved = self.cache_manager.get('unicode_key')
        self.assertEqual(retrieved, unicode_data)


class TestCacheManagerIntegration(unittest.TestCase):
    """Integration tests for cache manager with other components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = CacheManager(cache_dir=self.temp_dir)
    
    def tearDown(self):
        """Clean up after tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_manager_singleton_behavior(self):
        """Test that cache_manager behaves as expected singleton."""
        # The global cache_manager should be accessible
        from src.data.cache_manager import cache_manager
        
        # Should be a CacheManager instance
        self.assertIsInstance(cache_manager, CacheManager)
        
        # Should have expected methods
        self.assertTrue(hasattr(cache_manager, 'get'))
        self.assertTrue(hasattr(cache_manager, 'set'))
        self.assertTrue(hasattr(cache_manager, 'is_expired'))
    
    def test_cache_with_real_data_patterns(self):
        """Test cache with realistic data patterns from the application."""
        # Test price data pattern
        price_data = {
            'AAPL': {'current_price': 196.45, 'change': -2.75, 'change_percent': -1.38},
            'MSFT': {'current_price': 441.58, 'change': 5.23, 'change_percent': 1.20}
        }
        
        self.cache_manager.set('prices_batch_current', price_data, ttl_minutes=5)
        retrieved = self.cache_manager.get('prices_batch_current')
        self.assertEqual(retrieved, price_data)
        
        # Test news data pattern
        news_data = [
            {
                'headline': 'Apple Reports Strong Earnings',
                'url': 'https://example.com/news1',
                'datetime': '2025-01-15 10:30:00',
                'ticker': 'AAPL'
            }
        ]
        
        self.cache_manager.set('news_AAPL', news_data, ttl_minutes=15)
        retrieved = self.cache_manager.get('news_AAPL')
        self.assertEqual(retrieved, news_data)


if __name__ == '__main__':
    unittest.main()
