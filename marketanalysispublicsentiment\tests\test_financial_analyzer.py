"""
Unit tests for the financial analyzer module.

Tests the main orchestration and analysis pipeline including:
- Data fetching coordination
- Analysis pipeline management
- Integration between components
- Command-line argument processing
"""

import pytest
import unittest
from unittest.mock import patch, Mo<PERSON>, MagicMock
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.financial_analyzer import (
    fetch_all_data,
    analyze_all_data,
    main
)


class TestFinancialAnalyzer(unittest.TestCase):
    """Test cases for financial analyzer orchestration."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Sample data that would be returned by data fetchers
        self.sample_news_data = [
            {
                'headline': 'Apple Reports Strong Earnings',
                'text': 'Apple Inc. announced excellent quarterly results',
                'ticker': 'AAPL',
                'url': 'https://example.com/apple-news',
                'datetime': '2025-01-15 10:30:00'
            },
            {
                'headline': 'Tesla Announces New Model',
                'text': 'Tesla unveils innovative new electric vehicle',
                'ticker': 'TSLA',
                'url': 'https://example.com/tesla-news',
                'datetime': '2025-01-15 09:15:00'
            }
        ]
        
        self.sample_government_data = [
            {
                'headline': 'Federal Reserve Maintains Rates',
                'text': 'The Fed decided to keep interest rates unchanged',
                'url': 'https://federalreserve.gov/news',
                'category': 'monetary'
            }
        ]
        
        self.sample_market_data = {
            'SPY': {'name': 'S&P 500', 'price_change': 1.2},
            'QQQ': {'name': 'NASDAQ', 'price_change': 1.8},
            'DIA': {'name': 'Dow Jones', 'price_change': 0.5}
        }
    
    @patch('src.core.financial_analyzer.fetch_market_news_parallel')
    @patch('src.core.financial_analyzer.fetch_government_news_parallel')
    @patch('src.core.financial_analyzer.get_market_data_optimized')
    def test_fetch_all_data_normal_mode(self, mock_market_data, mock_gov_news, mock_market_news):
        """Test data fetching in normal mode."""
        # Mock the return values
        mock_market_news.return_value = (self.sample_news_data, {'total': 2, 'cached': 0})
        mock_gov_news.return_value = (self.sample_government_data, {'total': 1, 'cached': 0})
        mock_market_data.return_value = self.sample_market_data
        
        # Test normal mode
        result = fetch_all_data(quick_mode=False)
        
        # Should return 6 elements: news_data, news_stats, government_data, policy_stats, market_data, market_historical_data
        self.assertEqual(len(result), 6)
        
        news_data, news_stats, government_data, policy_stats, market_data, market_historical_data = result
        
        # Verify data structure
        self.assertEqual(news_data, self.sample_news_data)
        self.assertEqual(government_data, self.sample_government_data)
        self.assertEqual(market_data, self.sample_market_data)
        
        # Verify stats
        self.assertIn('total', news_stats)
        self.assertIn('total', policy_stats)
        
        # Verify functions were called correctly
        mock_market_news.assert_called_once_with(quick_mode=False)
        mock_gov_news.assert_called_once()
        mock_market_data.assert_called_once()
    
    @patch('src.core.financial_analyzer.fetch_market_news_parallel')
    @patch('src.core.financial_analyzer.fetch_government_news_parallel')
    @patch('src.core.financial_analyzer.get_market_data_optimized')
    def test_fetch_all_data_quick_mode(self, mock_market_data, mock_gov_news, mock_market_news):
        """Test data fetching in quick mode."""
        # Mock the return values
        mock_market_news.return_value = (self.sample_news_data[:1], {'total': 1, 'cached': 0})
        mock_gov_news.return_value = (self.sample_government_data, {'total': 1, 'cached': 0})
        mock_market_data.return_value = self.sample_market_data
        
        # Test quick mode
        result = fetch_all_data(quick_mode=True)
        
        # Should still return 6 elements
        self.assertEqual(len(result), 6)
        
        # Verify quick mode was passed correctly
        mock_market_news.assert_called_once_with(quick_mode=True)
    
    @patch('src.core.financial_analyzer.analyze_multi_ticker_sentiment')
    @patch('src.core.financial_analyzer.analyze_policy_sentiment')
    @patch('src.core.financial_analyzer.calculate_market_metrics')
    @patch('src.core.financial_analyzer.analyze_market_health_optimized')
    def test_analyze_all_data_basic(self, mock_market_health, mock_market_metrics, 
                                   mock_policy_sentiment, mock_multi_ticker):
        """Test basic data analysis pipeline."""
        # Mock analysis results
        mock_multi_ticker.return_value = (
            [0.5, 0.3],  # sentiment_scores
            [{'polarity': 0.5}, {'polarity': 0.3}],  # sentiment_details
            []  # multi_ticker_articles
        )
        
        mock_policy_sentiment.return_value = {
            'policy_sentiment': 0.1,
            'policy_mood': 'Neutral',
            'total_policy_articles': 1
        }
        
        mock_market_metrics.return_value = {
            'market_mood': 'Positive',
            'average_sentiment': 0.4,
            'positive_percentage': 60,
            'negative_percentage': 20,
            'neutral_percentage': 20
        }
        
        mock_market_health.return_value = {
            'recommendation': 'CAUTIOUSLY OPTIMISTIC',
            'market_trend': 'Positive'
        }
        
        # Run analysis
        result = analyze_all_data(
            self.sample_news_data,
            self.sample_government_data,
            self.sample_market_data
        )
        
        # Should return 13 elements
        self.assertEqual(len(result), 13)
        
        # Verify all analysis functions were called
        mock_multi_ticker.assert_called_once()
        mock_policy_sentiment.assert_called_once_with(self.sample_government_data)
        mock_market_metrics.assert_called_once()
        mock_market_health.assert_called_once()
    
    def test_analyze_all_data_empty_inputs(self):
        """Test analysis with empty inputs."""
        # Should handle empty data gracefully
        result = analyze_all_data([], [], {})
        
        # Should still return 13 elements
        self.assertEqual(len(result), 13)
        
        # Extract key results
        sentiment_analysis, policy_analysis, market_health = result[:3]
        
        # Should have default/empty values
        self.assertIsInstance(sentiment_analysis, dict)
        self.assertIsInstance(policy_analysis, dict)
        self.assertIsInstance(market_health, dict)
    
    @patch('src.core.financial_analyzer.fetch_all_data')
    @patch('src.core.financial_analyzer.analyze_all_data')
    @patch('src.core.financial_analyzer.FinancialDashboard')
    def test_main_function_dashboard_mode(self, mock_dashboard, mock_analyze, mock_fetch):
        """Test main function in dashboard mode."""
        # Mock data fetching and analysis
        mock_fetch.return_value = (
            self.sample_news_data, {'total': 2}, 
            self.sample_government_data, {'total': 1},
            self.sample_market_data, None
        )
        
        mock_analyze.return_value = tuple([{} for _ in range(13)])  # 13 empty dicts
        
        # Mock dashboard
        mock_dashboard_instance = Mock()
        mock_dashboard.return_value = mock_dashboard_instance
        
        # Test with dashboard mode (default)
        with patch('sys.argv', ['financial_analyzer.py']):
            main()
        
        # Verify dashboard was created and run
        mock_dashboard.assert_called_once()
        mock_dashboard_instance.run.assert_called_once()
    
    @patch('src.core.financial_analyzer.fetch_all_data')
    @patch('src.core.financial_analyzer.analyze_all_data')
    @patch('builtins.print')
    def test_main_function_quick_mode(self, mock_print, mock_analyze, mock_fetch):
        """Test main function with quick mode flag."""
        # Mock data fetching and analysis
        mock_fetch.return_value = (
            self.sample_news_data, {'total': 2}, 
            self.sample_government_data, {'total': 1},
            self.sample_market_data, None
        )
        
        mock_analyze.return_value = tuple([{} for _ in range(13)])
        
        # Test with quick mode flag
        with patch('sys.argv', ['financial_analyzer.py', '--quick']):
            main()
        
        # Verify quick mode was passed to fetch_all_data
        mock_fetch.assert_called_with(quick_mode=True)
    
    @patch('src.core.financial_analyzer.fetch_all_data')
    def test_fetch_all_data_error_handling(self, mock_fetch_market_news):
        """Test error handling in data fetching."""
        # Mock an exception in one of the fetch functions
        with patch('src.core.financial_analyzer.fetch_market_news_parallel', 
                  side_effect=Exception("API Error")):
            
            # Should handle the exception gracefully
            try:
                result = fetch_all_data()
                # If it doesn't raise an exception, that's good
                self.assertIsNotNone(result)
            except Exception as e:
                # If it does raise an exception, it should be handled appropriately
                self.fail(f"fetch_all_data should handle exceptions gracefully: {e}")
    
    def test_analyze_all_data_with_none_inputs(self):
        """Test analysis with None inputs."""
        # Should handle None inputs gracefully
        result = analyze_all_data(None, None, None)
        
        # Should still return results
        self.assertEqual(len(result), 13)
        
        # Results should be valid structures
        for item in result:
            self.assertIsNotNone(item)


class TestFinancialAnalyzerIntegration(unittest.TestCase):
    """Integration tests for financial analyzer."""
    
    @patch('src.core.financial_analyzer.fetch_market_news_parallel')
    @patch('src.core.financial_analyzer.fetch_government_news_parallel')
    @patch('src.core.financial_analyzer.get_market_data_optimized')
    def test_full_pipeline_integration(self, mock_market_data, mock_gov_news, mock_market_news):
        """Test the complete data fetch and analysis pipeline."""
        # Mock realistic data
        mock_market_news.return_value = ([
            {
                'headline': 'Apple Stock Surges on Strong Earnings',
                'text': 'Apple reported excellent quarterly earnings beating expectations',
                'ticker': 'AAPL',
                'url': 'https://example.com/apple',
                'datetime': '2025-01-15 10:30:00'
            }
        ], {'total': 1, 'cached': 0})
        
        mock_gov_news.return_value = ([
            {
                'headline': 'Fed Keeps Rates Steady',
                'text': 'Federal Reserve maintains current interest rate policy',
                'url': 'https://fed.gov/news',
                'category': 'monetary'
            }
        ], {'total': 1, 'cached': 0})
        
        mock_market_data.return_value = {
            'SPY': {'name': 'S&P 500', 'price_change': 1.5}
        }
        
        # Run complete pipeline
        fetch_result = fetch_all_data(quick_mode=True)
        news_data, news_stats, government_data, policy_stats, market_data, market_historical_data = fetch_result
        
        # Run analysis
        analysis_result = analyze_all_data(news_data, government_data, market_data)
        
        # Verify pipeline completed successfully
        self.assertEqual(len(fetch_result), 6)
        self.assertEqual(len(analysis_result), 13)
        
        # Verify data flow
        self.assertIsInstance(news_data, list)
        self.assertIsInstance(government_data, list)
        self.assertIsInstance(market_data, dict)
        
        # Verify analysis results structure
        sentiment_analysis, policy_analysis, market_health = analysis_result[:3]
        self.assertIsInstance(sentiment_analysis, dict)
        self.assertIsInstance(policy_analysis, dict)
        self.assertIsInstance(market_health, dict)


if __name__ == '__main__':
    unittest.main()
