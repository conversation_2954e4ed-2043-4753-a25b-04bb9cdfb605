"""
Unit tests for the earnings fetcher module (fixed version).

Tests earnings data fetching and analysis including:
- Quarterly earnings data retrieval
- Earnings trend analysis
- Multiple ticker earnings fetching
- Caching integration
"""

import pytest
import unittest
from unittest.mock import patch, Mo<PERSON>, MagicMock
import sys
import os
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.earnings_fetcher import (
    get_ticker_quarterly_earnings,
    cached_get_ticker_quarterly_earnings,
    get_multiple_ticker_earnings,
    analyze_earnings_trends,
    get_earnings_summary_for_ticker
)


class TestEarningsFetcher(unittest.TestCase):
    """Test cases for earnings fetcher functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Sample earnings data structure
        self.sample_earnings_data = {
            'ticker': 'AAPL',
            'last_updated': '2025-01-15T10:30:00',
            'quarters': [
                {
                    'quarter': 'Q1 2025',
                    'revenue': 123.9,
                    'net_income': 34.6,
                    'eps': 2.18,
                    'gross_profit': 56.3,
                    'operating_income': 40.2
                },
                {
                    'quarter': 'Q4 2024',
                    'revenue': 119.6,
                    'net_income': 33.9,
                    'eps': 2.12,
                    'gross_profit': 54.1,
                    'operating_income': 38.7
                },
                {
                    'quarter': 'Q3 2024',
                    'revenue': 94.9,
                    'net_income': 22.9,
                    'eps': 1.46,
                    'gross_profit': 42.8,
                    'operating_income': 26.3
                }
            ],
            'current_metrics': {
                'market_cap': 3012000000000,
                'pe_ratio': 28.5,
                'forward_pe': 26.2
            }
        }
        
        self.declining_earnings_data = {
            'ticker': 'DECLINING',
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 80.0, 'net_income': 8.0},
                {'quarter': 'Q4 2024', 'revenue': 90.0, 'net_income': 12.0},
                {'quarter': 'Q3 2024', 'revenue': 100.0, 'net_income': 15.0}
            ]
        }
    
    @patch('src.core.earnings_fetcher.yf.Ticker')
    def test_get_ticker_quarterly_earnings_success(self, mock_ticker):
        """Test successful quarterly earnings retrieval."""
        # Mock yfinance Ticker
        mock_stock = Mock()
        mock_ticker.return_value = mock_stock
        
        # Mock quarterly financials data
        mock_financials = Mock()
        mock_financials.empty = False
        mock_financials.columns = ['2024-12-31', '2024-09-30', '2024-06-30']
        
        # Mock the data access
        mock_financials.loc = {
            'Total Revenue': Mock(dropna=Mock(return_value=[123.9e9, 119.6e9, 94.9e9])),
            'Net Income': Mock(dropna=Mock(return_value=[34.6e9, 33.9e9, 22.9e9])),
            'Basic EPS': Mock(dropna=Mock(return_value=[2.18, 2.12, 1.46])),
            'Gross Profit': Mock(dropna=Mock(return_value=[56.3e9, 54.1e9, 42.8e9])),
            'Operating Income': Mock(dropna=Mock(return_value=[40.2e9, 38.7e9, 26.3e9]))
        }
        
        mock_stock.quarterly_financials = mock_financials
        mock_stock.info = {
            'marketCap': 3012000000000,
            'trailingPE': 28.5,
            'forwardPE': 26.2
        }
        
        # Test the function
        result = get_ticker_quarterly_earnings('AAPL')
        
        # Verify result structure
        self.assertIn('ticker', result)
        self.assertIn('last_updated', result)
        self.assertIn('quarters', result)
        self.assertIn('current_metrics', result)
        
        self.assertEqual(result['ticker'], 'AAPL')
        self.assertGreater(len(result['quarters']), 0)
        
        # Verify quarter data structure
        first_quarter = result['quarters'][0]
        self.assertIn('quarter', first_quarter)
        self.assertIn('revenue', first_quarter)
        self.assertIn('net_income', first_quarter)
    
    @patch('src.core.earnings_fetcher.yf.Ticker')
    def test_get_ticker_quarterly_earnings_no_data(self, mock_ticker):
        """Test earnings retrieval when no data is available."""
        # Mock yfinance Ticker with no data
        mock_stock = Mock()
        mock_ticker.return_value = mock_stock
        mock_stock.quarterly_financials = None
        
        result = get_ticker_quarterly_earnings('INVALID_TICKER')
        
        # Should return empty dict
        self.assertEqual(result, {})
    
    @patch('src.core.earnings_fetcher.yf.Ticker')
    def test_get_ticker_quarterly_earnings_exception(self, mock_ticker):
        """Test earnings retrieval with exception."""
        # Mock yfinance to raise exception
        mock_ticker.side_effect = Exception("API Error")
        
        result = get_ticker_quarterly_earnings('ERROR_TICKER')
        
        # Should return empty dict
        self.assertEqual(result, {})
    
    def test_analyze_earnings_trends_improving(self):
        """Test earnings trend analysis for improving company."""
        trends = analyze_earnings_trends(self.sample_earnings_data)
        
        # Check that trends are calculated
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
        self.assertIn('revenue_growth_rate', trends)
        self.assertIn('income_growth_rate', trends)
        self.assertIn('eps_trend', trends)
        
        # Revenue should be improving (positive growth)
        self.assertGreater(trends['revenue_growth_rate'], 0)
        self.assertEqual(trends['revenue_trend'], 'Improving')
        
        # Income should be improving
        self.assertGreater(trends['income_growth_rate'], 0)
        self.assertEqual(trends['income_trend'], 'Improving')
    
    def test_analyze_earnings_trends_declining(self):
        """Test earnings trend analysis for declining company."""
        trends = analyze_earnings_trends(self.declining_earnings_data)
        
        # Revenue should be declining
        self.assertLess(trends['revenue_growth_rate'], 0)
        self.assertEqual(trends['revenue_trend'], 'Declining')
        
        # Income should be declining
        self.assertLess(trends['income_growth_rate'], 0)
        self.assertEqual(trends['income_trend'], 'Declining')
    
    def test_analyze_earnings_trends_insufficient_data(self):
        """Test earnings trend analysis with insufficient data."""
        insufficient_data = {
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 100.0, 'net_income': 10.0}
            ]
        }
        
        trends = analyze_earnings_trends(insufficient_data)
        
        # Should handle gracefully with default values
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
        self.assertEqual(trends['revenue_trend'], 'Stable')
        self.assertEqual(trends['income_trend'], 'Stable')
    
    def test_analyze_earnings_trends_empty_data(self):
        """Test earnings trend analysis with empty data."""
        empty_data = {'quarters': []}
        
        trends = analyze_earnings_trends(empty_data)
        
        # Should handle empty data gracefully
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
        self.assertEqual(trends['revenue_trend'], 'No Data')
        self.assertEqual(trends['income_trend'], 'No Data')
    
    @patch('src.core.earnings_fetcher.cached_get_ticker_quarterly_earnings')
    def test_get_multiple_ticker_earnings(self, mock_cached_get):
        """Test fetching earnings for multiple tickers."""
        # Mock cached earnings data
        def mock_earnings_side_effect(ticker):
            if ticker == 'AAPL':
                return self.sample_earnings_data
            elif ticker == 'MSFT':
                return {'ticker': 'MSFT', 'quarters': []}
            else:
                return {}
        
        mock_cached_get.side_effect = mock_earnings_side_effect
        
        # Test with multiple tickers
        tickers = ['AAPL', 'MSFT', 'INVALID']
        result = get_multiple_ticker_earnings(tickers)
        
        # Should return data for all tickers
        self.assertEqual(len(result), 3)
        self.assertIn('AAPL', result)
        self.assertIn('MSFT', result)
        self.assertIn('INVALID', result)
        
        # AAPL should have data
        self.assertEqual(result['AAPL'], self.sample_earnings_data)
        
        # INVALID should have empty data
        self.assertEqual(result['INVALID'], {})
    
    @patch('src.core.earnings_fetcher.cache_manager')
    def test_cached_get_ticker_quarterly_earnings(self, mock_cache):
        """Test cached earnings retrieval."""
        # Mock cache miss
        mock_cache.get_or_fetch.return_value = (self.sample_earnings_data, False)
        
        result = cached_get_ticker_quarterly_earnings('AAPL')
        
        # Should return the earnings data
        self.assertEqual(result, self.sample_earnings_data)
        
        # Should have called cache
        mock_cache.get_or_fetch.assert_called_once()
    
    @patch('src.core.earnings_fetcher.cache_manager')
    def test_cached_get_ticker_quarterly_earnings_cache_hit(self, mock_cache):
        """Test cached earnings retrieval with cache hit."""
        # Mock cache hit
        mock_cache.get_or_fetch.return_value = (self.sample_earnings_data, True)
        
        result = cached_get_ticker_quarterly_earnings('AAPL')
        
        # Should return cached data
        self.assertEqual(result, self.sample_earnings_data)
    
    @patch('src.core.earnings_fetcher.cached_get_ticker_quarterly_earnings')
    @patch('src.core.earnings_fetcher.analyze_earnings_trends')
    def test_get_earnings_summary_for_ticker(self, mock_analyze_trends, mock_cached_get):
        """Test comprehensive earnings summary generation."""
        # Mock cached earnings data
        mock_cached_get.return_value = self.sample_earnings_data
        
        # Mock trends analysis
        mock_trends = {
            'revenue_trend': 'Improving',
            'income_trend': 'Improving',
            'revenue_growth_rate': 8.5,
            'income_growth_rate': 12.3
        }
        mock_analyze_trends.return_value = mock_trends
        
        # Test summary generation
        result = get_earnings_summary_for_ticker('AAPL')
        
        # Verify summary structure
        self.assertIn('ticker', result)
        self.assertIn('status', result)
        self.assertIn('raw_data', result)
        self.assertIn('analysis', result)
        self.assertIn('latest_quarter', result)
        self.assertIn('current_metrics', result)
        
        self.assertEqual(result['ticker'], 'AAPL')
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['raw_data'], self.sample_earnings_data)
        self.assertEqual(result['analysis'], mock_trends)
    
    @patch('src.core.earnings_fetcher.cached_get_ticker_quarterly_earnings')
    def test_get_earnings_summary_for_ticker_no_data(self, mock_cached_get):
        """Test earnings summary when no data is available."""
        # Mock no data
        mock_cached_get.return_value = {}
        
        result = get_earnings_summary_for_ticker('INVALID_TICKER')
        
        # Should return no_data status
        self.assertEqual(result['ticker'], 'INVALID_TICKER')
        self.assertEqual(result['status'], 'no_data')
    
    def test_earnings_trend_calculation_edge_cases(self):
        """Test earnings trend calculation edge cases."""
        # Test with zero values
        zero_data = {
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 0, 'net_income': 0},
                {'quarter': 'Q4 2024', 'revenue': 100, 'net_income': 10}
            ]
        }
        
        trends = analyze_earnings_trends(zero_data)
        
        # Should handle zero values
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)
        
        # Test with negative values
        negative_data = {
            'quarters': [
                {'quarter': 'Q1 2025', 'revenue': 100, 'net_income': -5},
                {'quarter': 'Q4 2024', 'revenue': 90, 'net_income': -10}
            ]
        }
        
        trends = analyze_earnings_trends(negative_data)
        
        # Should handle negative values
        self.assertIn('revenue_trend', trends)
        self.assertIn('income_trend', trends)


class TestEarningsFetcherIntegration(unittest.TestCase):
    """Integration tests for earnings fetcher."""
    
    @patch('src.core.earnings_fetcher.yf.Ticker')
    @patch('src.core.earnings_fetcher.cache_manager')
    def test_earnings_pipeline_integration(self, mock_cache, mock_ticker):
        """Test the complete earnings fetching and analysis pipeline."""
        # Mock yfinance data
        mock_stock = Mock()
        mock_ticker.return_value = mock_stock
        
        mock_financials = Mock()
        mock_financials.empty = False
        mock_financials.columns = ['2024-12-31', '2024-09-30']
        mock_financials.loc = {
            'Total Revenue': Mock(dropna=Mock(return_value=[120e9, 110e9])),
            'Net Income': Mock(dropna=Mock(return_value=[25e9, 20e9]))
        }
        
        mock_stock.quarterly_financials = mock_financials
        mock_stock.info = {'marketCap': 1000000000000}
        
        # Mock cache miss
        mock_cache.get_or_fetch.return_value = (None, False)
        
        # Test complete pipeline
        summary = get_earnings_summary_for_ticker('TEST')
        
        # Should complete successfully
        self.assertEqual(summary['ticker'], 'TEST')
        self.assertIn('status', summary)


if __name__ == '__main__':
    unittest.main()
