#!/usr/bin/env python3
"""
Test script to verify all modules are working correctly
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_imports():
    """Test that all modules can be imported"""
    try:
        print("Testing imports...")
        
        from src.config import config
        print("✅ config.py imported successfully")

        from src.data import data_fetcher
        print("✅ data_fetcher.py imported successfully")

        from src.core import sentiment_analyzer
        print("✅ sentiment_analyzer.py imported successfully")

        from src.core import policy_analyzer
        print("✅ policy_analyzer.py imported successfully")

        from src.ui import display_utils
        print("✅ display_utils.py imported successfully")
        
        print("\n🎉 All modules imported successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_config():
    """Test configuration module"""
    try:
        print("\nTesting configuration...")
        
        from src.config.config import MAJOR_TICKERS, GOVERNMENT_RSS_FEEDS, POLICY_KEYWORDS
        
        print(f"✅ Found {len(MAJOR_TICKERS)} major tickers")
        print(f"✅ Found {len(GOVERNMENT_RSS_FEEDS)} government RSS feeds")
        print(f"✅ Found {len(POLICY_KEYWORDS)} policy keyword categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test error: {e}")
        return False


def test_basic_functions():
    """Test basic functionality of key functions"""
    try:
        print("\nTesting basic functions...")
        
        # Test sentiment analysis
        from src.core.sentiment_analyzer import analyze_sentiment_batch
        sample_news = [
            {'headline': 'Apple reports strong earnings', 'text': 'Great results'},
            {'headline': 'Market decline continues', 'text': 'Bad news'}
        ]
        scores, details = analyze_sentiment_batch(sample_news)
        print(f"✅ Sentiment analysis working: {len(scores)} scores generated")
        
        # Test policy classification
        from src.core.policy_analyzer import classify_policy_impact
        sample_article = {
            'headline': 'Federal Reserve raises interest rates',
            'text': 'The FOMC decided to increase rates',
            'impact_weight': 1.0
        }
        impact = classify_policy_impact(sample_article)
        print(f"✅ Policy impact classification working: {impact['impact_level']} impact")
        
        # Test display utilities
        from src.ui.display_utils import create_hyperlink
        link = create_hyperlink('https://example.com', 'Test Link')
        print("✅ Display utilities working")
        
        return True
        
    except Exception as e:
        print(f"❌ Function test error: {e}")
        return False


def main():
    """Run all tests"""
    print("🧪 TESTING FINANCIAL ANALYZER MODULES")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    if test_imports():
        tests_passed += 1
    
    if test_config():
        tests_passed += 1
    
    if test_basic_functions():
        tests_passed += 1
    
    print(f"\n📊 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The modular system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    main()
