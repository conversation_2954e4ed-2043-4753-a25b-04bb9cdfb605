#!/usr/bin/env python3
"""
Investigate what caused Dominion Energy's Q4 2024 loss
"""

import yfinance as yf
from datetime import datetime, <PERSON><PERSON><PERSON>

def investigate_dominion_q4_loss():
    """Look into what might have caused the Q4 2024 loss"""
    
    print("🔍 INVESTIGATING DOMINION ENERGY Q4 2024 LOSS")
    print("=" * 60)
    
    # Get Dominion Energy stock info
    ticker = yf.Ticker("D")
    
    try:
        # Get recent news around Q4 2024 earnings
        print("\n📰 RECENT NEWS AND EVENTS:")
        print("-" * 40)
        
        # Get company info
        info = ticker.info
        company_name = info.get('longName', 'Dominion Energy')
        sector = info.get('sector', 'Unknown')
        industry = info.get('industry', 'Unknown')
        
        print(f"Company: {company_name}")
        print(f"Sector: {sector}")
        print(f"Industry: {industry}")
        
        # Get financial highlights
        print(f"\n💰 KEY FINANCIAL METRICS:")
        print("-" * 40)
        
        market_cap = info.get('marketCap')
        if market_cap:
            print(f"Market Cap: ${market_cap/1e9:.1f}B")
        
        pe_ratio = info.get('trailingPE')
        if pe_ratio:
            print(f"P/E Ratio: {pe_ratio:.1f}")
        
        dividend_yield = info.get('dividendYield')
        if dividend_yield:
            print(f"Dividend Yield: {dividend_yield*100:.1f}%")
        
        # Get quarterly financials
        print(f"\n📊 QUARTERLY FINANCIALS ANALYSIS:")
        print("-" * 40)
        
        quarterly_financials = ticker.quarterly_financials
        if not quarterly_financials.empty:
            print("Available financial data columns:")
            for col in quarterly_financials.columns[:4]:  # Last 4 quarters
                print(f"   {col.strftime('%Y-%m-%d')}")
        
        # Look for specific items that might explain the loss
        print(f"\n🔍 POTENTIAL CAUSES OF Q4 2024 LOSS:")
        print("-" * 40)
        
        print("Common reasons for utility company losses:")
        print("   1. 🌪️  Weather-related outages and repairs")
        print("   2. 🏭 Asset impairments or write-downs")
        print("   3. ⚖️  Legal settlements or regulatory penalties")
        print("   4. 🔧 Major maintenance or infrastructure investments")
        print("   5. 💸 One-time charges or restructuring costs")
        print("   6. 📉 Commodity price impacts (natural gas, coal)")
        print("   7. 🏛️  Regulatory changes or rate adjustments")
        
        # Utility-specific context
        print(f"\n🏭 UTILITY INDUSTRY CONTEXT:")
        print("-" * 40)
        print("Dominion Energy is a major utility company that:")
        print("   - Operates electric and gas utilities")
        print("   - Serves Virginia, North Carolina, South Carolina")
        print("   - Has significant infrastructure investments")
        print("   - Subject to regulatory oversight")
        print("   - Often has seasonal earnings patterns")
        
        print(f"\n❄️  Q4 SEASONAL FACTORS:")
        print("-" * 40)
        print("Q4 (Oct-Dec) considerations for utilities:")
        print("   - Higher heating demand (good for gas utilities)")
        print("   - Storm season preparations and repairs")
        print("   - Year-end asset evaluations and write-downs")
        print("   - Regulatory filing deadlines")
        print("   - Tax planning and adjustments")
        
    except Exception as e:
        print(f"Error getting detailed info: {e}")
    
    print(f"\n🎯 INVESTMENT PERSPECTIVE:")
    print("-" * 40)
    print("For utility stocks like Dominion Energy:")
    print("   ✅ Focus on long-term dividend sustainability")
    print("   ✅ Look at regulatory environment and rate cases")
    print("   ✅ Consider infrastructure investment cycles")
    print("   ⚠️  One-quarter losses are often temporary")
    print("   ⚠️  Percentage growth metrics can be misleading")
    
    print(f"\n📈 BETTER METRICS FOR UTILITIES:")
    print("-" * 40)
    print("   1. 💰 Dividend coverage ratio")
    print("   2. 📊 Rate base growth")
    print("   3. 🏭 Capital expenditure plans")
    print("   4. ⚖️  Regulatory approval status")
    print("   5. 🔌 Customer growth rates")
    print("   6. 💡 Renewable energy transition progress")

def show_corrected_analysis():
    """Show how the analysis should be presented"""
    
    print(f"\n" + "=" * 60)
    print("🔧 CORRECTED EARNINGS ANALYSIS")
    print("=" * 60)
    
    print(f"\n❌ MISLEADING: 'Inc Trend: 📈 Improving (+421%)'")
    print(f"✅ BETTER: 'Inc Trend: 🔄 Recovering (returned to profit)'")
    
    print(f"\n📊 IMPROVED DISPLAY:")
    print("-" * 30)
    print("💰 Earnings Status: 🔄 Recovery")
    print("📅 Q4 2024: -$76M loss")
    print("📅 Q1 2025: +$646M profit")
    print("💡 Analysis: Returned to profitability")
    
    print(f"\n🎯 CONTEXT INDICATORS:")
    print("-" * 30)
    print("   🟡 One-time loss detected")
    print("   📈 Historical profitability: 3 of 4 quarters positive")
    print("   🔄 Status: Recovery phase")
    print("   ⚠️  Percentage metrics may be misleading")

def main():
    investigate_dominion_q4_loss()
    show_corrected_analysis()
    
    print(f"\n" + "=" * 60)
    print("✅ SUMMARY")
    print("=" * 60)
    print("\nDominion Energy's 421% 'income trend' is:")
    print("   🧮 Mathematically accurate but misleading")
    print("   🔄 Actually represents recovery from temporary loss")
    print("   ⚠️  Highlights need for better trend analysis")
    print("   💡 Shows importance of context in financial metrics")
    print("\nYour skepticism was completely justified! 🎯")

if __name__ == "__main__":
    main()
