#!/usr/bin/env python3
"""
Cache monitoring and management utility
"""

import argparse
import sys
import os
# Add the parent directory to the path so we can import from src
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.data.cache_manager import cache_manager

def show_cache_stats():
    """Display comprehensive cache statistics"""
    print("📊 CACHE PERFORMANCE STATISTICS")
    print("=" * 60)
    
    stats = cache_manager.get_cache_stats()
    
    print(f"💾 Cache Files: {stats['cache_files']}")
    print(f"📏 Cache Size: {stats['cache_size_mb']:.2f} MB")
    print()
    
    print("🕒 Cache TTL Settings:")
    for data_type, ttl_minutes in stats['cache_ttl'].items():
        hours = ttl_minutes / 60
        if hours >= 24:
            print(f"  {data_type:15}: {ttl_minutes:4} min ({hours/24:.1f} days)")
        elif hours >= 1:
            print(f"  {data_type:15}: {ttl_minutes:4} min ({hours:.1f} hours)")
        else:
            print(f"  {data_type:15}: {ttl_minutes:4} min")
    print()
    
    print("📈 API Request Statistics (Last 24 Hours):")
    request_stats = stats['request_stats']
    total_requests = sum(request_stats.values())
    
    if total_requests > 0:
        for endpoint, count in sorted(request_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_requests) * 100
            print(f"  {endpoint:25}: {count:4} requests ({percentage:5.1f}%)")
        print(f"\n  {'TOTAL':25}: {total_requests:4} requests")
        
        # Calculate potential savings
        print(f"\n💰 Estimated API Call Reduction:")
        print(f"  Without caching: ~{total_requests * 3:,} calls (estimated)")
        print(f"  With caching: {total_requests:,} calls")
        print(f"  Savings: ~{((total_requests * 3 - total_requests) / (total_requests * 3)) * 100:.1f}%")
    else:
        print("  No requests recorded in the last 24 hours")

def show_cache_efficiency():
    """Show cache hit/miss ratios"""
    print("\n🎯 CACHE EFFICIENCY ANALYSIS")
    print("=" * 60)
    
    # This would need to be implemented with hit/miss tracking
    print("Cache efficiency tracking will be available after first run with cached system.")

def clear_cache_interactive():
    """Interactive cache clearing"""
    print("\n🗑️ CACHE MANAGEMENT")
    print("=" * 60)
    
    print("Options:")
    print("1. Clear all cache")
    print("2. Clear cache older than 24 hours")
    print("3. Clear cache older than 1 week")
    print("4. Clear specific data type")
    print("5. Cancel")
    
    choice = input("\nSelect option (1-5): ").strip()
    
    if choice == "1":
        cache_manager.clear_cache()
        print("✅ All cache cleared")
    elif choice == "2":
        cache_manager.clear_cache(older_than_hours=24)
        print("✅ Cache older than 24 hours cleared")
    elif choice == "3":
        cache_manager.clear_cache(older_than_hours=168)  # 1 week
        print("✅ Cache older than 1 week cleared")
    elif choice == "4":
        print("\nAvailable data types:")
        for data_type in cache_manager.cache_ttl.keys():
            print(f"  - {data_type}")
        
        data_type = input("\nEnter data type to clear: ").strip()
        if data_type in cache_manager.cache_ttl:
            cache_manager.clear_cache(data_type=data_type)
            print(f"✅ {data_type} cache cleared")
        else:
            print("❌ Invalid data type")
    elif choice == "5":
        print("❌ Cancelled")
    else:
        print("❌ Invalid option")

def estimate_api_savings():
    """Estimate API call savings from caching"""
    print("\n💡 API CALL REDUCTION ESTIMATES")
    print("=" * 60)
    
    # Typical usage patterns
    scenarios = {
        "Quick Mode (20 tickers)": {
            "news_calls": 20,
            "price_calls": 40,  # current + change
            "company_calls": 20,
            "market_calls": 6,
            "refreshes_per_hour": 12  # Every 5 minutes
        },
        "Full Mode (130 tickers)": {
            "news_calls": 130,
            "price_calls": 260,  # current + change
            "company_calls": 130,
            "market_calls": 6,
            "refreshes_per_hour": 6  # Every 10 minutes
        }
    }
    
    for scenario_name, scenario in scenarios.items():
        print(f"\n📊 {scenario_name}:")
        
        calls_per_refresh = (scenario["news_calls"] + scenario["price_calls"] + 
                           scenario["company_calls"] + scenario["market_calls"])
        calls_per_hour = calls_per_refresh * scenario["refreshes_per_hour"]
        calls_per_day = calls_per_hour * 24
        
        # With caching (estimated reduction)
        cached_calls_per_hour = (
            scenario["news_calls"] * 0.2 +  # 80% cache hit for news
            scenario["price_calls"] * 0.3 +  # 70% cache hit for prices
            scenario["company_calls"] * 0.05 +  # 95% cache hit for company names
            scenario["market_calls"] * 0.4  # 60% cache hit for market data
        ) * scenario["refreshes_per_hour"]
        
        cached_calls_per_day = cached_calls_per_hour * 24
        
        savings_per_day = calls_per_day - cached_calls_per_day
        savings_percentage = (savings_per_day / calls_per_day) * 100
        
        print(f"  Without caching: {calls_per_day:,} API calls/day")
        print(f"  With caching:    {cached_calls_per_day:,.0f} API calls/day")
        print(f"  Daily savings:   {savings_per_day:,.0f} calls ({savings_percentage:.1f}%)")

def main():
    """Main cache monitoring interface"""
    parser = argparse.ArgumentParser(description="Cache monitoring and management utility")
    parser.add_argument("--stats", action="store_true", help="Show cache statistics")
    parser.add_argument("--clear", action="store_true", help="Interactive cache clearing")
    parser.add_argument("--estimate", action="store_true", help="Show API savings estimates")
    parser.add_argument("--all", action="store_true", help="Show all information")
    
    args = parser.parse_args()
    
    if args.all or not any([args.stats, args.clear, args.estimate]):
        show_cache_stats()
        show_cache_efficiency()
        estimate_api_savings()
        
        if not args.all:
            print("\n" + "=" * 60)
            print("💡 Use --clear for cache management options")
    else:
        if args.stats:
            show_cache_stats()
            show_cache_efficiency()
        
        if args.estimate:
            estimate_api_savings()
        
        if args.clear:
            clear_cache_interactive()

if __name__ == "__main__":
    main()
