#!/usr/bin/env python3
"""
Deep diagnostic script to understand the yfinance news issue
"""

import yfinance as yf
import requests
import json

def test_direct_yahoo_api():
    """Test direct Yahoo Finance API calls"""
    print("🔍 Testing direct Yahoo Finance API calls...")
    print("=" * 60)
    
    # Try the direct Yahoo Finance news endpoint
    ticker = "AAPL"
    
    # This is the URL pattern that yfinance typically uses
    base_url = "https://query1.finance.yahoo.com/v1/finance/search"
    news_url = f"https://query2.finance.yahoo.com/v1/finance/search?q={ticker}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"📡 Testing direct API call for {ticker}...")
        response = requests.get(news_url, headers=headers, timeout=10)
        print(f"  📊 Status Code: {response.status_code}")
        print(f"  📏 Response Length: {len(response.text)} characters")
        print(f"  📋 Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"  ✅ Valid JSON response")
                print(f"  🔑 Top-level keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                # Look for news in the response
                if 'news' in data:
                    news_items = data['news']
                    print(f"  📰 News items found: {len(news_items)}")
                else:
                    print(f"  ⚠️ No 'news' key in response")
                    print(f"  📄 Response preview: {str(data)[:200]}...")
                    
            except json.JSONDecodeError as e:
                print(f"  ❌ Invalid JSON: {e}")
                print(f"  📄 Response preview: {response.text[:200]}...")
        else:
            print(f"  ❌ HTTP Error: {response.status_code}")
            print(f"  📄 Response preview: {response.text[:200]}...")
            
    except Exception as e:
        print(f"  ❌ Request failed: {e}")

def test_yfinance_internals():
    """Test yfinance internal methods"""
    print("\n🔧 Testing yfinance internal methods...")
    print("=" * 60)
    
    ticker = "AAPL"
    stock = yf.Ticker(ticker)
    
    # Check what methods are available
    methods = [attr for attr in dir(stock) if not attr.startswith('_')]
    print(f"📋 Available public methods: {methods}")
    
    # Try to access internal news-related attributes
    internal_attrs = [attr for attr in dir(stock) if 'news' in attr.lower()]
    print(f"🔍 News-related attributes: {internal_attrs}")
    
    # Try different approaches to get news
    approaches = [
        ('stock.news', lambda: stock.news),
        ('stock.get_news()', lambda: stock.get_news() if hasattr(stock, 'get_news') else None),
    ]
    
    for name, func in approaches:
        try:
            print(f"\n🧪 Testing {name}...")
            result = func()
            if result is not None:
                print(f"  ✅ Success: {type(result)} with {len(result) if hasattr(result, '__len__') else 'unknown'} items")
            else:
                print(f"  ⚠️ Returned None")
        except Exception as e:
            print(f"  ❌ Failed: {e}")

def test_alternative_news_sources():
    """Test alternative news sources"""
    print("\n🌐 Testing alternative news sources...")
    print("=" * 60)
    
    # Try Alpha Vantage news (if available)
    print("📰 Alternative news sources to consider:")
    print("  1. Alpha Vantage News API")
    print("  2. NewsAPI.org")
    print("  3. Financial Modeling Prep")
    print("  4. Polygon.io")
    print("  5. IEX Cloud")
    print("  6. RSS feeds from financial sites")
    
    # Test a simple RSS feed approach
    try:
        import feedparser
        print("\n🔄 Testing RSS feed approach...")
        
        # Yahoo Finance RSS feed for a ticker
        rss_url = f"https://feeds.finance.yahoo.com/rss/2.0/headline?s=AAPL&region=US&lang=en-US"
        
        feed = feedparser.parse(rss_url)
        print(f"  📊 RSS Feed Status: {feed.get('status', 'Unknown')}")
        print(f"  📰 RSS Entries: {len(feed.entries) if hasattr(feed, 'entries') else 0}")
        
        if hasattr(feed, 'entries') and feed.entries:
            first_entry = feed.entries[0]
            print(f"  📄 First entry: {first_entry.get('title', 'No title')[:60]}...")
            
    except ImportError:
        print("  ⚠️ feedparser not available (pip install feedparser)")
    except Exception as e:
        print(f"  ❌ RSS test failed: {e}")

if __name__ == "__main__":
    test_direct_yahoo_api()
    test_yfinance_internals()
    test_alternative_news_sources()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS COMPLETE")
    print("=" * 60)
