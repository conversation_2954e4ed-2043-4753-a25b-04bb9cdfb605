"""
Unit tests for the sentiment analyzer module.

Tests sentiment analysis functionality including:
- Individual article sentiment analysis
- Batch sentiment processing
- Sentiment scoring and classification
- Ticker-specific sentiment aggregation
"""

import pytest
import unittest
from unittest.mock import patch, Mock
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.sentiment_analyzer import (
    analyze_sentiment_batch,
    get_ticker_sector,
    detect_ticker_mentions,
    analyze_sentiment_around_ticker,
    analyze_multi_ticker_sentiment,
    calculate_market_metrics
)


class TestSentimentAnalyzer(unittest.TestCase):
    """Test cases for sentiment analyzer functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.positive_article = {
            'headline': 'Apple Reports Record Quarterly Earnings',
            'text': 'Apple Inc. announced excellent quarterly results with strong growth.',
            'ticker': 'AAPL'
        }
        
        self.negative_article = {
            'headline': 'Tesla Faces Production Challenges',
            'text': 'Tesla is experiencing significant production delays and quality issues.',
            'ticker': 'TSLA'
        }
        
        self.neutral_article = {
            'headline': 'Microsoft Maintains Steady Performance',
            'text': 'Microsoft reported results that were in line with analyst expectations.',
            'ticker': 'MSFT'
        }
    
    def test_analyze_sentiment_batch_basic(self):
        """Test basic sentiment analysis batch processing."""
        articles = [self.positive_article, self.negative_article, self.neutral_article]
        
        scores, details = analyze_sentiment_batch(articles)
        
        # Check that we get the right number of results
        self.assertEqual(len(scores), 3)
        self.assertEqual(len(details), 3)
        
        # Check that scores are in valid range [-1, 1]
        for score in scores:
            self.assertGreaterEqual(score, -1.0)
            self.assertLessEqual(score, 1.0)
        
        # Check that details contain required fields
        for detail in details:
            self.assertIn('polarity', detail)
            self.assertIn('category', detail)
    
    def test_analyze_sentiment_batch_empty_input(self):
        """Test sentiment analysis with empty input."""
        scores, details = analyze_sentiment_batch([])
        
        self.assertEqual(len(scores), 0)
        self.assertEqual(len(details), 0)
    
    def test_analyze_sentiment_batch_missing_fields(self):
        """Test sentiment analysis with articles missing required fields."""
        incomplete_article = {'headline': 'Test headline'}  # Missing 'text'
        
        scores, details = analyze_sentiment_batch([incomplete_article])
        
        # Should handle gracefully and return some result
        self.assertEqual(len(scores), 1)
        self.assertEqual(len(details), 1)
    
    def test_detect_ticker_mentions(self):
        """Test ticker detection in text."""
        # Test text with ticker mentions
        text_with_tickers = "AAPL reported strong earnings while MSFT announced new products"
        tickers = detect_ticker_mentions(text_with_tickers)

        self.assertIn('AAPL', tickers)
        self.assertIn('MSFT', tickers)

        # Test text without ticker mentions
        text_without_tickers = "The market performed well today"
        tickers = detect_ticker_mentions(text_without_tickers)

        self.assertEqual(len(tickers), 0)

    def test_analyze_sentiment_around_ticker(self):
        """Test sentiment analysis around specific ticker mentions."""
        text = "Apple Inc. (AAPL) reported excellent quarterly results with strong growth"

        result = analyze_sentiment_around_ticker(text, 'AAPL')

        # Check that result contains expected fields
        self.assertIn('sentiment_score', result)
        self.assertIn('context_snippets', result)
        self.assertIn('mention_count', result)

        # Should find at least one mention
        self.assertGreater(result['mention_count'], 0)

        # Sentiment should be positive for "excellent" and "strong growth"
        self.assertGreater(result['sentiment_score'], 0)

    def test_calculate_market_metrics(self):
        """Test market metrics calculation."""
        # Test with mixed sentiment scores
        sentiment_scores = [0.5, -0.3, 0.1, -0.7, 0.8]
        sentiment_details = [
            {'polarity': score, 'category': 'Positive' if score > 0.1 else 'Negative' if score < -0.1 else 'Neutral'}
            for score in sentiment_scores
        ]

        metrics = calculate_market_metrics(sentiment_scores, sentiment_details)

        # Check that metrics contain expected fields
        self.assertIn('market_mood', metrics)
        self.assertIn('average_sentiment', metrics)
        self.assertIn('positive_percentage', metrics)
        self.assertIn('negative_percentage', metrics)
        self.assertIn('neutral_percentage', metrics)

        # Check that percentages add up to 100
        total_pct = (metrics['positive_percentage'] +
                    metrics['negative_percentage'] +
                    metrics['neutral_percentage'])
        self.assertAlmostEqual(total_pct, 100, places=1)
    
    def test_get_ticker_sector(self):
        """Test ticker sector lookup functionality."""
        # Test known tickers
        aapl_sector = get_ticker_sector('AAPL')
        self.assertIsInstance(aapl_sector, str)
        self.assertNotEqual(aapl_sector, '')
        
        # Test unknown ticker
        unknown_sector = get_ticker_sector('UNKNOWN_TICKER')
        self.assertEqual(unknown_sector, 'Other')
        
        # Test case sensitivity (function expects uppercase)
        lower_case_sector = get_ticker_sector('aapl')
        upper_case_sector = get_ticker_sector('AAPL')
        # Lower case should return 'Other' since it's not in the mapping
        self.assertEqual(lower_case_sector, 'Other')
        # Upper case should return the actual sector
        self.assertNotEqual(upper_case_sector, 'Other')
    
    def test_sentiment_consistency(self):
        """Test that sentiment analysis is consistent for identical inputs."""
        article = self.positive_article
        
        # Run sentiment analysis multiple times
        scores1, _ = analyze_sentiment_batch([article])
        scores2, _ = analyze_sentiment_batch([article])
        scores3, _ = analyze_sentiment_batch([article])
        
        # Results should be consistent (within small tolerance for floating point)
        self.assertAlmostEqual(scores1[0], scores2[0], places=3)
        self.assertAlmostEqual(scores2[0], scores3[0], places=3)
    
    def test_sentiment_relative_ordering(self):
        """Test that sentiment analysis correctly orders positive vs negative content."""
        articles = [self.positive_article, self.negative_article]
        scores, _ = analyze_sentiment_batch(articles)
        
        # Positive article should have higher sentiment than negative article
        self.assertGreater(scores[0], scores[1])
    
    def test_analyze_multi_ticker_sentiment(self):
        """Test multi-ticker sentiment analysis."""
        # Create articles with multiple ticker mentions
        multi_ticker_articles = [
            {
                'headline': 'AAPL and MSFT Partnership Announced',
                'text': 'Apple and Microsoft announced a strategic partnership',
                'ticker': 'AAPL'  # Primary ticker
            },
            {
                'headline': 'Tech Sector Rally: GOOGL, AMZN, TSLA Up',
                'text': 'Google, Amazon, and Tesla all saw significant gains',
                'ticker': 'GOOGL'
            }
        ]

        scores, details, multi_articles = analyze_multi_ticker_sentiment(multi_ticker_articles)

        # Should process all articles
        self.assertEqual(len(scores), 2)
        self.assertEqual(len(details), 2)

        # Should detect multi-ticker articles
        self.assertGreaterEqual(len(multi_articles), 0)
    
    def test_large_batch_processing(self):
        """Test sentiment analysis with a large batch of articles."""
        # Create a large batch of articles
        large_batch = [self.positive_article.copy() for _ in range(100)]
        
        scores, details = analyze_sentiment_batch(large_batch)
        
        # Check that all articles were processed
        self.assertEqual(len(scores), 100)
        self.assertEqual(len(details), 100)
        
        # Check that all scores are valid
        for score in scores:
            self.assertIsInstance(score, (int, float))
            self.assertGreaterEqual(score, -1.0)
            self.assertLessEqual(score, 1.0)


class TestSentimentAnalyzerIntegration(unittest.TestCase):
    """Integration tests for sentiment analyzer with real data."""
    
    def test_real_world_headlines(self):
        """Test sentiment analysis with real-world style headlines."""
        real_headlines = [
            {
                'headline': 'Apple Stock Soars After Beating Earnings Expectations',
                'text': 'Apple Inc. reported quarterly earnings that exceeded analyst expectations.',
                'ticker': 'AAPL'
            },
            {
                'headline': 'Tesla Recalls Thousands of Vehicles Due to Safety Concerns',
                'text': 'Tesla is recalling vehicles due to potential safety issues.',
                'ticker': 'TSLA'
            },
            {
                'headline': 'Microsoft Announces Dividend Increase',
                'text': 'Microsoft Corporation announced a quarterly dividend increase.',
                'ticker': 'MSFT'
            }
        ]
        
        scores, details = analyze_sentiment_batch(real_headlines)
        
        # Should process all articles
        self.assertEqual(len(scores), 3)
        
        # First article (positive) should have highest sentiment
        # Second article (negative) should have lowest sentiment
        self.assertGreater(scores[0], scores[1])  # Apple > Tesla
        self.assertGreater(scores[2], scores[1])  # Microsoft > Tesla


if __name__ == '__main__':
    unittest.main()
