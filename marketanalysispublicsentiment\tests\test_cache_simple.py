"""
Simple unit tests for the cache manager module.

Tests the actual cache manager functionality as implemented.
"""

import pytest
import unittest
import sys
import os
import tempfile
import shutil

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.cache_manager import CacheManager, cache_manager


class TestCacheManagerSimple(unittest.TestCase):
    """Simple test cases for cache manager functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary directory for cache testing
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = CacheManager(cache_dir=self.temp_dir)
        
        # Sample data for testing
        self.test_data = {
            'ticker': 'AAPL',
            'price': 196.45,
            'timestamp': '2025-01-15 10:30:00'
        }
    
    def tearDown(self):
        """Clean up after each test method."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_data_and_retrieve(self):
        """Test basic cache data storage and retrieval."""
        # Cache data
        self.cache_manager.cache_data('prices', 'AAPL', self.test_data)
        
        # Retrieve data from cache
        retrieved_data = self.cache_manager.get_cached_data('prices', 'AAPL')
        self.assertEqual(retrieved_data, self.test_data)
    
    def test_cache_validity_check(self):
        """Test cache validity checking."""
        # Cache some data
        self.cache_manager.cache_data('prices', 'AAPL', self.test_data)
        
        # Should be valid immediately after caching
        is_valid = self.cache_manager.is_cache_valid('prices', 'AAPL')
        self.assertTrue(is_valid)
        
        # Non-existent cache should be invalid
        is_valid_nonexistent = self.cache_manager.is_cache_valid('prices', 'NONEXISTENT')
        self.assertFalse(is_valid_nonexistent)
    
    def test_get_or_fetch_cache_hit(self):
        """Test get_or_fetch with cache hit."""
        # Pre-cache some data
        self.cache_manager.cache_data('prices', 'AAPL', self.test_data)
        
        # Define a fetch function that should not be called
        def fetch_function():
            self.fail("Fetch function should not be called on cache hit")
        
        # Should return cached data
        result, cache_hit = self.cache_manager.get_or_fetch('prices', 'AAPL', fetch_function)
        
        self.assertEqual(result, self.test_data)
        self.assertTrue(cache_hit)
    
    def test_get_or_fetch_cache_miss(self):
        """Test get_or_fetch with cache miss."""
        # Define a fetch function that returns test data
        def fetch_function():
            return self.test_data
        
        # Should call fetch function and cache result
        result, cache_hit = self.cache_manager.get_or_fetch('prices', 'MSFT', fetch_function)
        
        self.assertEqual(result, self.test_data)
        self.assertFalse(cache_hit)
        
        # Verify data was cached
        cached_data = self.cache_manager.get_cached_data('prices', 'MSFT')
        self.assertEqual(cached_data, self.test_data)
    
    def test_cache_key_generation(self):
        """Test cache key generation."""
        key1 = self.cache_manager._get_cache_key('prices', 'AAPL')
        key2 = self.cache_manager._get_cache_key('prices', 'AAPL')
        key3 = self.cache_manager._get_cache_key('prices', 'MSFT')
        
        # Same inputs should generate same key
        self.assertEqual(key1, key2)
        
        # Different inputs should generate different keys
        self.assertNotEqual(key1, key3)
        
        # Keys should be strings
        self.assertIsInstance(key1, str)
        self.assertIsInstance(key3, str)
    
    def test_request_tracking(self):
        """Test request tracking functionality."""
        # Track some requests
        self.cache_manager.track_request('yfinance', 'news')
        self.cache_manager.track_request('yfinance', 'prices')
        self.cache_manager.track_request('yfinance', 'news')  # Duplicate
        
        # Get request statistics
        stats = self.cache_manager.get_request_stats()
        
        # Should have tracked requests
        self.assertIsInstance(stats, dict)
        # Note: stats might be empty if requests are older than 24 hours in test
    
    def test_cache_statistics(self):
        """Test cache statistics."""
        # Add some data to cache
        self.cache_manager.cache_data('prices', 'AAPL', self.test_data)
        self.cache_manager.cache_data('news', 'AAPL', {'articles': []})
        
        # Get statistics
        stats = self.cache_manager.get_cache_stats()
        
        # Check statistics structure
        self.assertIn('cache_files', stats)
        self.assertIn('cache_size_mb', stats)
        self.assertIn('request_stats', stats)
        
        # Should have at least 2 cache files
        self.assertGreaterEqual(stats['cache_files'], 2)
        self.assertGreater(stats['cache_size_mb'], 0)
    
    def test_cache_clear(self):
        """Test cache clearing functionality."""
        # Add some data to cache
        self.cache_manager.cache_data('prices', 'AAPL', self.test_data)
        self.cache_manager.cache_data('news', 'AAPL', {'articles': []})
        
        # Verify data is cached
        self.assertIsNotNone(self.cache_manager.get_cached_data('prices', 'AAPL'))
        self.assertIsNotNone(self.cache_manager.get_cached_data('news', 'AAPL'))
        
        # Clear cache
        self.cache_manager.clear_cache()
        
        # Verify cache is cleared
        self.assertIsNone(self.cache_manager.get_cached_data('prices', 'AAPL'))
        self.assertIsNone(self.cache_manager.get_cached_data('news', 'AAPL'))
    
    def test_batch_get_or_fetch(self):
        """Test batch get or fetch functionality."""
        # Define a batch fetch function
        def batch_fetch_function(identifiers):
            return {ticker: {'price': 100.0 + i} for i, ticker in enumerate(identifiers)}
        
        # Test batch operation
        tickers = ['AAPL', 'MSFT', 'GOOGL']
        results = self.cache_manager.batch_get_or_fetch('prices', tickers, batch_fetch_function)
        
        # Should return results for all tickers
        self.assertEqual(len(results), 3)
        self.assertIn('AAPL', results)
        self.assertIn('MSFT', results)
        self.assertIn('GOOGL', results)
        
        # Results should have expected structure
        self.assertEqual(results['AAPL']['price'], 100.0)
        self.assertEqual(results['MSFT']['price'], 101.0)
    
    def test_cache_with_different_data_types(self):
        """Test caching different types of data."""
        # Test with different data types
        string_data = "test string"
        list_data = [1, 2, 3, 'test']
        dict_data = {'key': 'value', 'number': 42}
        
        # Cache different data types
        self.cache_manager.cache_data('strings', 'test1', string_data)
        self.cache_manager.cache_data('lists', 'test2', list_data)
        self.cache_manager.cache_data('dicts', 'test3', dict_data)
        
        # Retrieve and verify
        self.assertEqual(self.cache_manager.get_cached_data('strings', 'test1'), string_data)
        self.assertEqual(self.cache_manager.get_cached_data('lists', 'test2'), list_data)
        self.assertEqual(self.cache_manager.get_cached_data('dicts', 'test3'), dict_data)


class TestCacheManagerIntegrationSimple(unittest.TestCase):
    """Simple integration tests for cache manager."""
    
    def test_global_cache_manager_access(self):
        """Test that the global cache manager is accessible and functional."""
        # Should be able to import and use the global cache manager
        self.assertIsInstance(cache_manager, CacheManager)
        
        # Should have expected methods
        self.assertTrue(hasattr(cache_manager, 'cache_data'))
        self.assertTrue(hasattr(cache_manager, 'get_cached_data'))
        self.assertTrue(hasattr(cache_manager, 'is_cache_valid'))
        self.assertTrue(hasattr(cache_manager, 'get_or_fetch'))
    
    def test_cache_manager_with_realistic_data(self):
        """Test cache manager with realistic application data."""
        # Test with realistic price data
        price_data = {
            'current_price': 196.45,
            'change': -2.75,
            'change_percent': -1.38,
            'volume': 45234567
        }
        
        # Cache and retrieve
        cache_manager.cache_data('prices', 'AAPL_current', price_data)
        retrieved = cache_manager.get_cached_data('prices', 'AAPL_current')
        
        self.assertEqual(retrieved, price_data)
        
        # Test with realistic news data
        news_data = [
            {
                'headline': 'Apple Reports Strong Earnings',
                'url': 'https://example.com/news1',
                'datetime': '2025-01-15 10:30:00',
                'ticker': 'AAPL'
            }
        ]
        
        cache_manager.cache_data('news', 'AAPL_latest', news_data)
        retrieved_news = cache_manager.get_cached_data('news', 'AAPL_latest')
        
        self.assertEqual(retrieved_news, news_data)


if __name__ == '__main__':
    unittest.main()
