"""
Pytest configuration and shared fixtures for the Financial Sentiment Analyzer test suite.

This file provides common test fixtures, mock data, and configuration
that can be used across all test modules.
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import from src
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Sample test data
SAMPLE_NEWS_ARTICLES = [
    {
        'headline': 'Apple Reports Record Quarterly Earnings',
        'text': 'Apple Inc. announced record-breaking quarterly earnings with strong iPhone sales.',
        'url': 'https://example.com/apple-earnings',
        'datetime': '2025-01-15 10:30:00',
        'time_ago': '2 hours ago',
        'source': 'Financial Times',
        'ticker': 'AAPL'
    },
    {
        'headline': 'Market Volatility Continues Amid Economic Uncertainty',
        'text': 'Stock markets experienced significant volatility as investors react to economic data.',
        'url': 'https://example.com/market-volatility',
        'datetime': '2025-01-15 09:15:00',
        'time_ago': '3 hours ago',
        'source': 'Reuters',
        'ticker': 'SPY'
    },
    {
        'headline': 'Tesla Announces New Manufacturing Plant',
        'text': 'Tesla will build a new manufacturing facility to meet growing demand.',
        'url': 'https://example.com/tesla-plant',
        'datetime': '2025-01-15 08:00:00',
        'time_ago': '4 hours ago',
        'source': 'Bloomberg',
        'ticker': 'TSLA'
    }
]

SAMPLE_POLICY_ARTICLES = [
    {
        'headline': 'Federal Reserve Raises Interest Rates by 0.25%',
        'text': 'The Federal Open Market Committee voted to increase the federal funds rate.',
        'url': 'https://example.com/fed-rates',
        'datetime': '2025-01-15 14:00:00',
        'time_ago': '1 hour ago',
        'source': 'Federal Reserve',
        'impact_weight': 1.0
    },
    {
        'headline': 'New Tax Policy Announced by Treasury Department',
        'text': 'The Treasury Department unveiled new tax policies affecting corporations.',
        'url': 'https://example.com/tax-policy',
        'datetime': '2025-01-15 11:00:00',
        'time_ago': '4 hours ago',
        'source': 'Treasury.gov',
        'impact_weight': 0.8
    }
]

SAMPLE_EARNINGS_DATA = {
    'AAPL': {
        'quarters': [
            {'quarter': 'Q1 2025', 'revenue': 123.9, 'net_income': 34.6},
            {'quarter': 'Q4 2024', 'revenue': 119.6, 'net_income': 33.9},
            {'quarter': 'Q3 2024', 'revenue': 94.9, 'net_income': 22.9},
            {'quarter': 'Q2 2024', 'revenue': 85.8, 'net_income': 21.4}
        ]
    },
    'MSFT': {
        'quarters': [
            {'quarter': 'Q1 2025', 'revenue': 65.6, 'net_income': 24.7},
            {'quarter': 'Q4 2024', 'revenue': 62.0, 'net_income': 22.0},
            {'quarter': 'Q3 2024', 'revenue': 61.9, 'net_income': 21.9},
            {'quarter': 'Q2 2024', 'revenue': 56.2, 'net_income': 20.1}
        ]
    }
}

SAMPLE_PRICE_DATA = {
    'AAPL': {
        'current_price': 196.45,
        'price_change': -2.75,
        'price_change_percent': -1.38,
        'volume': 45234567,
        'market_cap': 3012000000000
    },
    'MSFT': {
        'current_price': 441.58,
        'price_change': 5.23,
        'price_change_percent': 1.20,
        'volume': 23456789,
        'market_cap': 3287000000000
    }
}

@pytest.fixture
def sample_news_articles():
    """Fixture providing sample news articles for testing."""
    return SAMPLE_NEWS_ARTICLES.copy()

@pytest.fixture
def sample_policy_articles():
    """Fixture providing sample policy articles for testing."""
    return SAMPLE_POLICY_ARTICLES.copy()

@pytest.fixture
def sample_earnings_data():
    """Fixture providing sample earnings data for testing."""
    return SAMPLE_EARNINGS_DATA.copy()

@pytest.fixture
def sample_price_data():
    """Fixture providing sample price data for testing."""
    return SAMPLE_PRICE_DATA.copy()

@pytest.fixture
def mock_cache_manager():
    """Fixture providing a mocked cache manager."""
    with patch('src.data.cache_manager.cache_manager') as mock:
        mock.get.return_value = None
        mock.set.return_value = True
        mock.is_expired.return_value = True
        yield mock

@pytest.fixture
def mock_yfinance():
    """Fixture providing mocked yfinance responses."""
    with patch('yfinance.Ticker') as mock_ticker:
        mock_instance = Mock()
        mock_instance.news = SAMPLE_NEWS_ARTICLES
        mock_instance.info = {
            'shortName': 'Apple Inc.',
            'sector': 'Technology',
            'marketCap': 3012000000000,
            'currentPrice': 196.45
        }
        mock_instance.history.return_value = Mock()
        mock_ticker.return_value = mock_instance
        yield mock_ticker

@pytest.fixture
def mock_requests():
    """Fixture providing mocked HTTP requests."""
    with patch('requests.get') as mock_get:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'data': 'test'}
        mock_response.text = '<html><body>Test content</body></html>'
        mock_get.return_value = mock_response
        yield mock_get

@pytest.fixture
def temp_cache_dir(tmp_path):
    """Fixture providing a temporary cache directory."""
    cache_dir = tmp_path / "cache"
    cache_dir.mkdir()
    return cache_dir

@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_cache_dir):
    """Automatically set up test environment for all tests."""
    # Set test cache directory
    monkeypatch.setenv('CACHE_DIR', str(temp_cache_dir))
    
    # Mock external API calls by default
    with patch('time.sleep'):  # Speed up tests by mocking sleep
        yield

class TestDataBuilder:
    """Helper class for building test data with fluent interface."""
    
    @staticmethod
    def news_article(ticker='AAPL', sentiment='positive'):
        """Build a news article with specified characteristics."""
        base_article = SAMPLE_NEWS_ARTICLES[0].copy()
        base_article['ticker'] = ticker
        
        if sentiment == 'positive':
            base_article['headline'] = f'{ticker} Reports Strong Earnings Growth'
            base_article['text'] = 'Excellent performance with record profits'
        elif sentiment == 'negative':
            base_article['headline'] = f'{ticker} Faces Significant Challenges'
            base_article['text'] = 'Declining revenues and market concerns'
        elif sentiment == 'neutral':
            base_article['headline'] = f'{ticker} Maintains Steady Performance'
            base_article['text'] = 'Consistent results in line with expectations'
            
        return base_article
    
    @staticmethod
    def earnings_quarter(quarter='Q1 2025', revenue=100.0, net_income=25.0):
        """Build earnings data for a specific quarter."""
        return {
            'quarter': quarter,
            'revenue': revenue,
            'net_income': net_income
        }

@pytest.fixture
def test_data_builder():
    """Fixture providing the TestDataBuilder helper."""
    return TestDataBuilder
